package logger

import (
	"fmt"
	"os"
	"runtime"
	"time"
)

const (
	// LevelError 错误
	LevelError = iota
	// LevelWarning 警告
	LevelWarning
	// LevelInformational 提示
	LevelInformational
	// LevelDebug 除错
	LevelDebug
)

var logger *Logger

// HTTPPort log code.
const HTTPPort = "30084"

// Logger 日志
type Logger struct {
	level int
}

// Println 打印
func (ll *Logger) Println(msg string) {
	fmt.Printf("%s %s|||%s\r\n", time.Now().Format("2006/01/02 15:04:05"), HTTPPort, msg)
}

// LogCode With SLSCode
func (ll *Logger) LogCode(code string, msg string) {
	lName, _ := os.Hostname()
	fmt.Printf("[code]%s %v [hostname]%v [message]%s\r\n", code, time.Now().Format("2006-01-02 15:04:05"), lName, msg)
}

// Panic 极端错误
func (ll *Logger) Panic(format string, v ...interface{}) {
	if LevelError > ll.level {
		return
	}
	msg := fmt.Sprintf("[Panic] "+format, v...)
	ll.Println(msg)
	os.Exit(0)
}

// Error 错误
func (ll *Logger) Error(format string, v ...interface{}) {
	if LevelError > ll.level {
		return
	}
	_, file, line, _ := runtime.Caller(1)
	msg := fmt.Sprintf("[Error] %s:%d %s", file, line, fmt.Sprintf(format, v...))
	ll.Println(msg)
}

// Warning 警告
func (ll *Logger) Warning(format string, v ...interface{}) {
	if LevelWarning > ll.level {
		return
	}
	msg := fmt.Sprintf("[Warn] "+format, v...)
	ll.Println(msg)
}

// Info 信息
func (ll *Logger) Info(format string, v ...interface{}) {
	if LevelInformational > ll.level {
		return
	}
	msg := fmt.Sprintf("[Info] "+format, v...)
	ll.Println(msg)
}

// Debug 校验
func (ll *Logger) Debug(format string, v ...interface{}) {
	if LevelDebug > ll.level {
		return
	}
	msg := fmt.Sprintf("[Debug] "+format, v...)
	ll.Println(msg)
}

// Log 返回日志对象
func Log() *Logger {
	if logger == nil {
		l := Logger{
			level: LevelDebug,
		}
		logger = &l
	}
	return logger
}
