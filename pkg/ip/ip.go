package ip

import (
	"chongli/pkg/httpclient"
	"crypto/md5"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"
)

// Ip2Long 将 IPV4 的字符串互联网协议转换成长整型数字.
func Ip2Long(ipAddress string) (l uint32) {
	ip := net.ParseIP(ipAddress)
	if ip == nil {
		return
	}
	l = binary.BigEndian.Uint32(ip.To4())
	return
}

// Long2Ip 将长整型转化为字符串形式带点的互联网标准格式地址(IPV4).
func Long2Ip(properAddress uint32) (ips string) {
	ipByte := make([]byte, 4)
	binary.BigEndian.PutUint32(ipByte, properAddress)
	ips = net.IP(ipByte).String()
	return
}

// ==============================================================================

// LocationInfo IP地理位置信息
type LocationInfo struct {
	Country  string `json:"country"`
	Province string `json:"province"`
	City     string `json:"city"`
	District string `json:"district"`
}

// GetLocationByIP 通过IP获取地理位置
func GetLocationByIP(ip string) (*LocationInfo, error) {
	// 使用免费的IP定位API
	url := fmt.Sprintf("http://ip-api.com/json/%s?lang=zh-CN", ip)

	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result struct {
		Status     string `json:"status"`
		Country    string `json:"country"`
		RegionName string `json:"regionName"`
		City       string `json:"city"`
		District   string `json:"district"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	if result.Status != "success" {
		return nil, fmt.Errorf("IP定位失败")
	}

	return &LocationInfo{
		Country:  result.Country,
		Province: result.RegionName,
		City:     result.City,
		District: result.District,
	}, nil
}

// =========================================================================

// GetIPLocationV2 ip转地域 v2
func GetIPLocationV2(ip string) (string, error) {
	if ip == "" {
		return "", fmt.Errorf("IP地址不能为空")
	}

	baseURL := "https://ip-public.51wnl-cq.com/api/v2/do"
	ipMD5 := md5Lower(ip)

	// 构建查询参数
	queryStr := fmt.Sprintf("ip=%s", ip)

	// 设置请求头
	headers := map[string]string{
		"Authorization": "ip " + ipMD5,
	}

	// 发送HTTP GET请求
	resp, err := httpclient.HttpGet(baseURL, queryStr, headers, 10*time.Second)
	if err != nil {
		return "", fmt.Errorf("请求IP定位服务失败: %v", err)
	}

	// 解析响应
	var result struct {
		Code int    `json:"status"`
		Msg  string `json:"msg"`
		Data struct {
			Country   string `json:"country"`
			Province  string `json:"province"`
			City      string `json:"city"`
			Operators string `json:"operators"`
		} `json:"data"`
	}

	if err := json.Unmarshal([]byte(resp), &result); err != nil {
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查响应状态
	if result.Code != 0 {
		return "", fmt.Errorf("IP定位失败: %s", result.Msg)
	}

	return result.Data.Country + result.Data.Province + result.Data.City + result.Data.Operators, nil
}

func md5Lower(ip string) string {
	hash := md5.New()
	hash.Write([]byte(ip))
	md5Result := hash.Sum(nil)
	return strings.ToLower(hex.EncodeToString(md5Result))
}
