package ffmpeg

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"
)

func TestConvertToAccMP4(t *testing.T) {
	// 跳过测试如果ffmpeg未安装
	if !IsFFmpegInstalled() {
		t.<PERSON><PERSON>("FFmpeg未安装，跳过测试")
	}
	var err error

	// 创建临时目录用于测试文件
	tempDir := filepath.Join("/tmp", "ffmpeg_test")

	// defer os.RemoveAll(tempDir) // 测试结束后清理

	// 创建一个测试用的视频文件（这里只是创建一个空文件作为示例）
	// 在实际测试中，你应该使用一个真实的视频文件
	inputFile := filepath.Join(tempDir, "shengyin.mp4")
	outputFile := filepath.Join(tempDir, "test_output.mp4")

	// 模拟测试：如果没有真实的视频文件，这个测试会失败
	// 这里我们使用一个条件来控制是否执行实际转换
	executeRealConversion := true // 设置为true以执行实际转换

	if executeRealConversion {
		// 执行转换
		err = ConvertToAccMP4(inputFile, outputFile)
		if err != nil {
			t.Fatalf("转换失败: %v", err)
		}

		// 验证输出文件是否存在并检查文件大小
		fileInfo, err := os.Stat(outputFile)
		if os.IsNotExist(err) {
			t.Errorf("输出文件未创建")
		} else if err != nil {
			t.Errorf("无法获取输出文件信息: %v", err)
		} else {
			// 验证文件大小是否大于0
			if fileInfo.Size() <= 0 {
				t.Errorf("输出文件大小为0，可能转换失败")
			} else {
				t.Logf("输出文件大小: %d 字节", fileInfo.Size())
			}
		}
	} else {
		t.Log("跳过实际文件转换测试，需要有效的视频文件才能完成测试")
	}
}

// TestConvertToAccMP4_Error 测试转换过程中出现错误的情况
func TestConvertToAccMP4_Error(t *testing.T) {
	// 创建临时目录用于测试文件
	tempDir, err := os.MkdirTemp("", "ffmpeg_test")
	if err != nil {
		t.Fatalf("无法创建临时目录: %v", err)
	}
	defer os.RemoveAll(tempDir) // 测试结束后清理

	// 创建一个不存在的输入文件路径
	inputFile := filepath.Join(tempDir, "nonexistent_input.mp4")
	outputFile := filepath.Join(tempDir, "test_output.mp4")

	// 如果FFmpeg已安装，则执行测试
	if IsFFmpegInstalled() {
		// 执行转换，应该返回错误（因为输入文件不存在）
		err = ConvertToAccMP4(inputFile, outputFile)
		if err == nil {
			t.Error("对不存在的输入文件进行转换时，应该返回错误")
		} else {
			t.Logf("预期的错误: %v", err)
		}
	} else {
		t.Skip("FFmpeg未安装，跳过测试")
	}
}

// TestConvertToAccMP4_FFmpegNotInstalled 测试当FFmpeg未安装时的情况
func TestConvertToAccMP4_FFmpegNotInstalled(t *testing.T) {
	// 如果FFmpeg已安装，则模拟测试
	if IsFFmpegInstalled() {
		// 这里我们只能测试函数的第一个检查点
		// 创建一个模拟函数来测试未安装FFmpeg的情况
		err := simulateFFmpegNotInstalled("input.mp4", "output.mp4")
		if err == nil {
			t.Error("当FFmpeg未安装时，应该返回错误")
		}

		// 验证错误消息
		expectedErrMsg := "FFmpeg is not installed on the system"
		if err.Error() != expectedErrMsg {
			t.Errorf("错误消息不匹配，期望: %s, 实际: %s", expectedErrMsg, err.Error())
		}
	} else {
		// 如果系统上确实没有安装FFmpeg，直接测试函数
		err := ConvertToAccMP4("input.mp4", "output.mp4")
		if err == nil {
			t.Error("当FFmpeg未安装时，应该返回错误")
		}

		// 验证错误消息
		expectedErrMsg := "FFmpeg is not installed on the system"
		if err.Error() != expectedErrMsg {
			t.Errorf("错误消息不匹配，期望: %s, 实际: %s", expectedErrMsg, err.Error())
		}
	}
}

// simulateFFmpegNotInstalled 模拟FFmpeg未安装的情况
func simulateFFmpegNotInstalled(inputFile, outputFile string) error {
	// 直接返回未安装的错误，模拟ConvertToAccMP4函数的第一个检查点
	return fmt.Errorf("FFmpeg is not installed on the system")
}
