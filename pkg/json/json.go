package json

import (
	"bytes"
	jsonIter "github.com/json-iterator/go"
)

// Encode 对变量进行 JSON 编码并去除转移字符.
func Encode(val interface{}) (b []byte, err error) {
	var jsons = jsonIter.ConfigCompatibleWithStandardLibrary
	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := jsons.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	err = jsonEncoder.Encode(val)
	if err != nil {
		return
	}
	b = []byte(bf.String())
	return
}

// Decode 对 JSON 格式的字符串进行解码,注意val使用指针.
func Decode(data []byte, val interface{}) error {
	var jsons = jsonIter.ConfigCompatibleWithStandardLibrary
	return jsons.Unmarshal(data, val)
}
