package json

import (
	"testing"

	"github.com/tidwall/gjson"
)

var jsonExample = `{"k1":"v1","k2":"v2"}`

func TestJsonEncode(t *testing.T) {
	bJson, _err := Encode(jsonExample)
	if _err != nil {
		t.<PERSON>rf("Encode errors: %v\n", _err)
	}

	parse := gjson.Parse(string(bJson)).Bool()
	if !parse {
		t.<PERSON>("Encode ParseJson bool is %v \n", parse)
	}
}

func TestJsonDecode(t *testing.T) {
	var i interface{}
	_err := Decode([]byte(jsonExample), &i)
	if _err != nil {
		t.<PERSON><PERSON>("Decode unit test fail, errors: %v\n", _err)
	}
}
