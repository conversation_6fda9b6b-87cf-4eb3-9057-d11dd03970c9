package email

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tidwall/gjson"
	"github.com/valyala/fasthttp"
	"regexp"
	"time"

	"chongli/component/apollo"
	"chongli/pkg/httpclient"
)

const (
	// InternalEmail internal email.
	InternalEmail Type = iota + 1 // internal email.
	// NormalEmail normal email.
	NormalEmail // normal email.
	// InternalEmailRegex internal email regex.
	InternalEmailRegex = `^[a-zA-Z0-9]+@youloft.com$`
	// NormalEmailRegex normal email regex.
	NormalEmailRegex = `^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$`
	emailFormatErr   = "email format error"
	project          = "宠历"
)

var (
	// emailServerURL email transport url.
	emailServerURL string
	// regex_regexInternalInternal the regular expression of internal email. it's looks like "<EMAIL>"
	regexInternal = regexp.MustCompile(InternalEmailRegex)
	// _regexNormal the regular expression of normal email.
	regexNormal = regexp.MustCompile(NormalEmailRegex)
	_k          = "1w@S,t-d'^aejt13"
	_iv         = []byte{0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF}
)

type SendObject struct {
	Subject string `json:"subject"`
	Content string `json:"content"`
	To      string `json:"to"`
}

// Type email type.
type Type int

// InitEmailServer set email transport url from apollo config.
func InitEmailServer() {
	emailServerURL = apollo.GetApolloConfig().EmailServer
}

// SendEmail send email.
func SendEmail(sendObject SendObject, emailType Type) error {
	// verify email.
	result := ValidateEmail(sendObject.To, emailType)
	if !result {
		return errors.New(emailFormatErr)
	}

	body, errMarshall := json.Marshal(sendObject)
	if errMarshall != nil {
		return errMarshall
	}

	authorization := fmt.Sprintf("%X", md5.Sum([]byte(fmt.Sprintf("%s%s", sendObject.Subject, sendObject.To))))
	authorization = "email " + authorization

	resp, err := httpclient.HttpPost(emailServerURL, "", string(body), map[string]string{"Authorization": authorization}, 10*time.Second)
	if err != nil {
		fmt.Println(err.Error(), resp)
	}

	return err
}

// ValidateEmail validate email.
func ValidateEmail(email string, emailType Type) bool {
	// internal email.
	if emailType == InternalEmail {
		return regexInternal.Match([]byte(email))
	}

	// normalEmail email.
	return regexNormal.Match([]byte(email))
}

type AesBase64 struct {
	key []byte // 允许16,24,32字节长度
	iv  []byte // 只允许16字节长度
}

func (p AesBase64) Encrypt(text []byte) (string, error) {
	if len(text) == 0 {
		return "", nil
	}
	//生成数据块
	block, err := aes.NewCipher(p.key)
	if err != nil {
		return "", err
	}
	blockSize := block.BlockSize()
	originData := p.pad(text, blockSize)
	blockMode := cipher.NewCBCEncrypter(block, p.iv)
	crypted := make([]byte, len(originData))
	blockMode.CryptBlocks(crypted, originData)
	return base64.StdEncoding.EncodeToString(crypted), nil
}

func (p AesBase64) Decrypt(text string) ([]byte, error) {
	if len(text) == 0 {
		return []byte(text), nil
	}
	decodeData, err := base64.StdEncoding.DecodeString(text)
	if err != nil {
		return []byte(text), err
	}
	if len(decodeData) == 0 {
		return []byte(text), nil
	}
	//数据块
	block, _ := aes.NewCipher(p.key)
	blockMode := cipher.NewCBCDecrypter(block, p.iv)
	originData := make([]byte, len(decodeData))
	blockMode.CryptBlocks(originData, decodeData)
	return p.unPad(originData), nil
}

func (p AesBase64) pad(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padText...)
}

func (p AesBase64) unPad(ciphertext []byte) []byte {
	length := len(ciphertext)
	// 去掉最后一次的padding
	unPadding := int(ciphertext[length-1])
	return ciphertext[:(length - unPadding)]
}

func SendCode(email string) error {
	_m := map[string]interface{}{
		"mail":    email,
		"Project": project,
	}
	text, _ := json.Marshal(_m)
	_a := AesBase64{key: []byte(_k), iv: _iv}
	enc, _err := _a.Encrypt(text)
	if _err != nil {
		return _err
	}
	uri := "https://r.51wnl-cq.com/api/Services/PushWnlC"
	h := map[string]string{
		"Content-Type": "text/plain",
	}
	r, _err := httpPostClient(uri, "", enc, h, 5*time.Second)
	if _err != nil {
		return _err
	}
	success := gjson.Get(r, "success").Bool()
	if !success {
		message := gjson.Get(r, "msg").String()
		return fmt.Errorf(message)
	}
	return _err
}

func Verify(email, code string) error {
	uri := "https://r.51wnl-cq.com/api/Services/VerifiC"
	m := map[string]interface{}{
		"Project": project,
		"mail":    email,
		"code":    code,
	}
	h := map[string]string{
		"Content-Type": "application/json",
	}
	_a := AesBase64{key: []byte(_k), iv: _iv}
	text, _ := json.Marshal(m)
	enc, _err := _a.Encrypt(text)
	if _err != nil {
		return _err
	}
	r, _err := httpPostClient(uri, "", enc, h, 5*time.Second)
	if _err != nil {
		return _err
	}
	success := gjson.Get(r, "success").Bool()
	if !success {
		message := gjson.Get(r, "msg").String()
		return fmt.Errorf(message)
	}
	return _err
}

func httpPostClient(uri string, params string, body string, header map[string]string, timeout time.Duration) (r string, err error) {
	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)
	req.Header.SetMethod("POST")
	req.SetRequestURI(uri)
	req.URI().SetQueryString(params)
	// 小红书不支持压缩,否则将出现响应乱码
	if len(header) > 0 {
		for _key, _val := range header {
			req.Header.Set(_key, _val)
		}
	}
	requestBody := []byte(body)
	req.SetBody(requestBody)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp) // 用完需要释放资源
	fasthttpClient := &fasthttp.Client{
		MaxIdleConnDuration: timeout,
		MaxConnDuration:     timeout,
		ReadTimeout:         timeout,
		WriteTimeout:        timeout,
		MaxConnWaitTimeout:  timeout,
	}
	if err = fasthttpClient.DoTimeout(req, resp, fasthttpClient.ReadTimeout); err != nil {
		return
	}
	r = string(resp.Body())
	return
}
