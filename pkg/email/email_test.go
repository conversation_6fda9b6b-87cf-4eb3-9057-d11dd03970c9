package email

import (
	"chongli/pkg/httpclient"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
)

var testCasesForEmail = []struct {
	testName   string
	sendObject SendObject
	emailType  Type
	err        error
	patches    func() *gomonkey.Patches
}{
	{
		testName: "测试正确的内部邮箱发送",
		sendObject: SendObject{
			Subject: "Email Test",
			Content: "Hi, wangyihuan:\n\tThis email is a test, ignore it, pls.",
			To:      "<EMAIL>",
		},
		emailType: InternalEmail,
		err:       nil,
		patches: func() *gomonkey.Patches {
			return gomonkey.ApplyFunc(httpclient.HttpPost, func(uri string, params string, body string, header map[string]string, timeout time.Duration) (r string, err error) {
				return "", nil
			})
		},
	},
	{
		testName: "测试普通邮箱发送",
		sendObject: SendObject{
			Subject: "Email Test",
			Content: "Hi, wangyihuan:\n\tThis email is a test, ignore it, pls.",
			To:      "<EMAIL>",
		},
		emailType: NormalEmail,
		err:       nil,
		patches: func() *gomonkey.Patches {
			return gomonkey.ApplyFunc(httpclient.HttpPost, func(uri string, params string, body string, header map[string]string, timeout time.Duration) (r string, err error) {
				return "", nil
			})
		},
	},
	{
		testName: "测试错误的内部邮箱",
		sendObject: SendObject{
			Subject: "Email Test",
			Content: "Hi, wangyihuan:\n\tThis email is a test, ignore it, pls.",
			To:      "<EMAIL>",
		},
		emailType: InternalEmail,
		err:       errors.New("email format error"),
		patches: func() *gomonkey.Patches {
			return gomonkey.ApplyFunc(httpclient.HttpPost, func(uri string, params string, body string, header map[string]string, timeout time.Duration) (r string, err error) {
				return "", nil
			})
		},
	},
	{
		testName: "测试Http请求错误",
		sendObject: SendObject{
			Subject: "Email Test",
			Content: "Hi, wangyihuan:\n\tThis email is a test, ignore it, pls.",
			To:      "<EMAIL>",
		},
		emailType: InternalEmail,
		err:       errors.New("服务器异常"),
		patches: func() *gomonkey.Patches {
			return gomonkey.ApplyFunc(httpclient.HttpPost, func(uri string, params string, body string, header map[string]string, timeout time.Duration) (r string, err error) {
				return "服务器异常", errors.New("服务器异常")
			})
		},
	},

	{
		testName: "测试Json Marshall错误",
		sendObject: SendObject{
			Subject: "Email Test",
			Content: "Hi, wangyihuan:\n\tThis email is a test, ignore it, pls.",
			To:      "<EMAIL>",
		},
		emailType: NormalEmail,
		err:       errors.New("json marshal error"),
		patches: func() *gomonkey.Patches {
			return gomonkey.ApplyFunc(httpclient.HttpPost, func(uri string, params string, body string, header map[string]string, timeout time.Duration) (r string, err error) {
				return "", nil
			}).ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
				return []byte{}, errors.New("json marshal error")
			})
		},
	},
}

func TestSendEmail(t *testing.T) {
	Convey("测试邮箱发送", t, func() {
		for _, testCase := range testCasesForEmail {
			Convey(testCase.testName, func() {
				if testCase.patches != nil {
					defer testCase.patches().Reset()
				}
				err := SendEmail(testCase.sendObject, testCase.emailType)
				So(err, ShouldEqual, testCase.err)
			})
		}
	})
}

func TestEmail(t *testing.T) {
	err := SendEmail(SendObject{
		Subject: "test email",
		Content: "hi, I'm wangyihuan.",
		To:      "<EMAIL>",
	}, InternalEmail)
	if err != nil {
		t.Error(err)
	}
}
