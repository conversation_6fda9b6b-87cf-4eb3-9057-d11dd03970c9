package aliyun

import (
	"chongli/component/apollo"
	"chongli/pkg/httpclient"
	"chongli/pkg/logger"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/green"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

// ImageSyncScanRequest 阿里云图片鉴黄
func ImageSyncScanRequest(sign string, dataID int, imageURI string) (result bool, err error) {
	apolloConfig := apollo.GetApolloConfig()
	client, cr := green.NewClientWithAccessKey(
		apolloConfig.GreenRegionID,
		apolloConfig.GreenAccessKeyID,
		apolloConfig.GreenAccessKeySecret)
	if cr != nil {
		err = cr
		logger.Log().LogCode("aliyun_green", fmt.Sprintf("NewClientWithAccessKey 初始化错误: [%v]", cr.Error()))
		return
	}
	// 业务标识
	//dataIDString := uuid.New().String() + "-" + sign + "-" + strconv.Itoa(dataID)
	dataIDString := fmt.Sprintf("%v-%v-%v", uuid.New().String(), sign, strconv.Itoa(dataID))
	taskParameter := map[string]interface{}{
		"dataId": dataIDString,
		"url":    imageURI,
	}
	// scenes:检测场景,支持指定以下多个场景
	// porn:图片智能鉴黄,terrorism:图片暴恐涉政,ad:图文违规,qrcode:图片二维码,live:图片不良场景,logo:图片logo
	contents, _ := json.Marshal(
		map[string]interface{}{
			"tasks":   taskParameter,
			"scenes":  [...]string{"porn"},
			"bizType": apolloConfig.GreenBizType,
		},
	)
	request := green.CreateImageSyncScanRequest()
	request.SetContent(contents)
	response, _err := client.ImageSyncScan(request)
	if _err != nil {
		err = _err
		logger.Log().LogCode("aliyun_green", fmt.Sprintf("NewClientWithAccessKey ImageSyncScan 错误:[%v]", _err.Error()))
		return
	}
	if response.GetHttpStatus() != 200 {
		logger.Log().LogCode("aliyun_green", fmt.Sprintf("NewClientWithAccessKey response not success. status: [%v]", response.GetHttpStatus()))
		return
	}
	responses := response.GetHttpContentString()
	// pass:结果正常,无需进行其余操作
	// review:结果不确定,需要进行人工审核
	// block:结果违规,建议直接删除或者限制公开
	if gjson.Get(responses, "data.0.results.0.suggestion").String() == "pass" {
		result = true
		return
	}
	//else {
	//	// 失败的丢进sls
	//	var log LogConfig
	//	var requestUri []interface{}
	//	requestUri = append(requestUri, dataId)
	//	requestUri = append(requestUri, sign)
	//	requestUri = append(requestUri, imageUri)
	//	requestUriByte, _ := json.Marshal(requestUri)
	//	go log.SetLog(string(requestUriByte), responseJson).SendLog()
	//}
	return
}

// TextSyncScanRequest 阿里云文本检测
func TextSyncScanRequest(sign string, dataID int, toBeDetected string) (result bool, err error) {
	apolloConfig := apollo.GetApolloConfig()

	client, _err := green.NewClientWithAccessKey(apolloConfig.GreenRegionID,
		apolloConfig.GreenAccessKeyID,
		apolloConfig.GreenAccessKeySecret)
	if _err != nil {
		err = _err
		logger.Log().LogCode("aliyun_green", fmt.Sprintf("TextSyncScanRequest New Client Error [%v]", _err.Error()))
		return
	}
	dataIDString := fmt.Sprintf("%v-%v-%v", uuid.New().String(), sign, strconv.Itoa(dataID))
	taskParameter := map[string]interface{}{
		"content": toBeDetected,
		"dataId":  dataIDString,
	}
	// scenes:检测场景,唯一取值:antispam
	content, _ := json.Marshal(
		map[string]interface{}{
			"scenes":  [...]string{"antispam"},
			"tasks":   [...]map[string]interface{}{taskParameter},
			"bizType": apolloConfig.GreenBizType,
		},
	)
	textScanRequest := green.CreateTextScanRequest()
	textScanRequest.SetContent(content)
	textScanResponse, _err := client.TextScan(textScanRequest)
	if _err != nil {
		// TODO 如果阿里云检测报错,则调用自家内容安全Api
		//err = _err
		logger.Log().LogCode("aliyun_green", fmt.Sprintf("TextSyncScanRequest textScanResponse Error [%v]", _err.Error()))
		midBody, _ := sjson.Set("", "ver", "1")
		midBody, _ = sjson.Set(midBody, "words", toBeDetected)
		midResponse, _err := httpclient.HttpPost(apolloConfig.GreenMiddlewareAPI, "", midBody, nil, 0)
		if _err != nil {
			err = _err
			logger.Log().LogCode("aliyun_green", fmt.Sprintf("TextSyncScanRequest GreenMidApi Error [%v]", _err.Error()))
			return
		}
		midCode := gjson.Get(midResponse, "code").Int()
		midSuggestion := gjson.Get(midResponse, "data.suggestion").String()
		if midCode == 20000 && midSuggestion == "pass" {
			result = true
			return
		}
		return
	}
	if textScanResponse.GetHttpStatus() != 200 {
		logger.Log().LogCode("aliyun_green", fmt.Sprintf("TextSyncScanRequest response not success. status: [%v]", textScanResponse.GetHttpStatus()))
		err = errors.New("阿里云文本校验sdk请求异常" + toBeDetected)
		return
	}
	responseJSON := textScanResponse.GetHttpContentString()
	suggestionText := gjson.Get(responseJSON, "data.0.results.0.suggestion").String()
	if suggestionText == "pass" {
		result = true
		return
	}
	return
}
