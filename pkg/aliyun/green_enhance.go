package aliyun

import (
	"chongli/component/apollo"
	"encoding/json"
	"fmt"
	"strings"
	"sync"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	green20220302 "github.com/alibabacloud-go/green-20220302/v2/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
)

// 项目组就近选择对应的服务地址
const (
	// EndPointPeking 华北2(北京)
	EndPointPeking = `green-cip.cn-beijing.aliyuncs.com`
	// EndPointShanghai 华东2(上海)
	EndPointShanghai = `green-cip.cn-shanghai.aliyuncs.com`
	// EndPointSingapore 新加坡
	EndPointSingapore = `green-cip.ap-southeast-1.aliyuncs.com`
)

// ServiceName 服务名称, 用于区分项目组调用
type ServiceName struct {
	// greenServiceName  在阿里云控制后台复制现有content service.
	greenContentServiceName string
	// greenServiceName  在阿里云控制后台复制现有image service.
	greenImageServiceName string
	// greenVoiceServiceName 在阿里云控制后台复制现有voice service.
	greenVoiceServiceName string
	// greenVideoServiceName 在阿里云控制后台复制现有video service.
	greenVideoServiceName string
	// greenFileServiceName 在阿里云控制后台复制现有file service.
	greenFileServiceName string
	// greenURLServiceName 在阿里云控制后台复制现有URL service.
	greenURLServiceName string
}

var (
	// greenClient 客户端
	greenClient *green20220302.Client
	// serviceName 服务名称
	serviceName ServiceName
)

// InitGreenClient 使用AccessKey、AccessKeySecret和ServiceName初始化账号Client.
func InitGreenClient() (err error) {
	apolloConfig := apollo.GetApolloConfig()
	// 初始化serviceName
	serviceName.greenContentServiceName = apolloConfig.ContentServiceName // content serviceName 从apollo中获取.
	serviceName.greenImageServiceName = apolloConfig.ImageServiceName     // image serviceName 从apollo中获取.
	serviceName.greenVoiceServiceName = apolloConfig.VoiceServiceName     // voice serviceName 从apollo中获取.
	serviceName.greenVideoServiceName = apolloConfig.VideoServiceName     // video serviceName 从apollo中获取.
	serviceName.greenFileServiceName = apolloConfig.FileServiceName       // file serviceName 从apollo中获取.
	serviceName.greenURLServiceName = apolloConfig.URLServiceName         // URL serviceName 从apollo中获取.

	config := &openapi.Config{
		AccessKeyId:     tea.String(apolloConfig.GreenAccessKeyID),     // accessKeyID 从apollo中获取.
		AccessKeySecret: tea.String(apolloConfig.GreenAccessKeySecret), // accessKeySecret 从apollo中获取.
	}
	// Endpoint 请参考 https://api.aliyun.com/product/Green
	config.Endpoint = tea.String(EndPointPeking) // 因为服务器在青岛, 故这里就近选择了北京.

	greenClient = &green20220302.Client{}
	greenClient, err = green20220302.NewClient(config)

	return err
}

// 文本检测增强版
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/TextModeration?tab=DOC
func textDetection(content string) (response *green20220302.TextModerationResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}

	textModerationRequest := &green20220302.TextModerationRequest{}

	// serviceParameters is a json, like below:
	// {
	// 		"content": "FUCK YOU!"
	// }
	serviceParameters := make(map[string]string)
	// 设置待检测文本
	serviceParameters["content"] = content

	serviceParametersBytes, _err := json.Marshal(serviceParameters)
	if _err != nil {
		return nil, _err
	}

	textModerationRequest.SetService(serviceName.greenContentServiceName)
	textModerationRequest.SetServiceParameters(string(serviceParametersBytes))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, _err = greenClient.TextModerationWithOptions(textModerationRequest, runtime)
		if _err != nil {
			return _err
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, _err = util.AssertAsString(error.Message)
		if _err != nil {
			return nil, _err
		}
	}

	return
}

// 文本检测增强版
func textDetectionPlus(content string) (response *green20220302.TextModerationPlusResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}
	textModerationRequest := &green20220302.TextModerationPlusRequest{}
	serviceParameters := make(map[string]string)
	// 设置待检测文本
	serviceParameters["content"] = content
	serviceParametersBytes, _err := json.Marshal(serviceParameters)
	if _err != nil {
		return nil, _err
	}
	// 设置 service name，从apollo拉取的配置
	textModerationRequest.SetService(serviceName.greenContentServiceName)
	textModerationRequest.SetServiceParameters(string(serviceParametersBytes))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, _err = greenClient.TextModerationPlusWithOptions(textModerationRequest, runtime)
		if _err != nil {
			return _err
		}

		return nil
	}()
	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		_ = d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, _err = util.AssertAsString(error.Message)
		if _err != nil {
			return nil, _err
		}
	}

	return
}

// TextDetection 文本检测
func TextDetection(text string) (result string, err error) {
	resp, _err := textDetectionPlus(text)
	if _err != nil {
		return "", _err
	}
	if resp == nil {
		return "", nil
	}
	// 歌词违规提醒
	if result = handleTextDetectionPlusLabels(resp.Body.Data.Result); result != "" {
		return result, nil
	}
	return "", nil
}

// 检测后的处理
func handleTextDetectionPlusLabels(results []*green20220302.TextModerationPlusResponseBodyDataResult) string {
	aliErrs := map[string]string{
		"pornographic_adult":           "疑似色情内容",
		"sexual_terms":                 "疑似性健康内容",
		"sexual_suggestive":            "疑似低俗内容",
		"political_figure":             "疑似政治人物",
		"political_entity":             "疑似政治实体",
		"political_n":                  "疑似敏感政治内容",
		"political_p":                  "疑似涉政禁宣人物",
		"political_a":                  "涉政专项升级保障",
		"violent_extremist":            "疑似极端组织",
		"violent_incidents":            "疑似极端主义内容",
		"violent_weapons":              "疑似武器弹药",
		"contraband_drug":              "疑似毒品相关",
		"contraband_gambling":          "疑似赌博相关",
		"contraband_act":               "疑似违禁行为",
		"contraband_entity":            "疑似违禁工具",
		"inappropriate_discrimination": "疑似偏见歧视内容",
		"inappropriate_ethics":         "疑似不良价值观内容",
		"inappropriate_profanity":      "疑似攻击辱骂内容",
		"inappropriate_oral":           "疑似低俗口头语内容",
		"inappropriate_superstition":   "疑似封建迷信内容",
		"inappropriate_nonsense":       "疑似无意义灌水内容",
		"pt_to_sites":                  "疑似站外引流",
		"pt_by_recruitment":            "疑似网赚兼职广告",
		"pt_to_contact":                "疑似引流广告号",
		"religion_b":                   "疑似涉及佛教",
		"religion_t":                   "疑似涉及道教",
		"religion_c":                   "疑似涉及基督教",
		"religion_i":                   "疑似涉及伊斯兰教",
		"religion_h":                   "疑似涉及印度教",
		"customized":                   "命中自定义词库",
	}
	responseErr := ""
	for _, result := range results {
		labels := strings.Split(*result.Label, ",")
		if len(labels) > 0 {
			for _, label := range labels {
				if aliErr, ok := aliErrs[label]; ok {
					responseErr += aliErr + ";"
				}
			}
		}
	}
	return responseErr
}

// ImageDetection 图片检测增强版
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/ImageModeration
func ImageDetection(imageURL string) (response *green20220302.ImageModerationResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}

	imageModerationRequest := &green20220302.ImageModerationRequest{}

	// serviceParameters is a json, like below:
	// {
	//     "imageUrl": "https://img.alicdn.com/tfs/TB1U4r9AeH2gK0jSZJnXXaT1FXa-2880-480.png",
	//     "dataId": "img1234567"
	// }
	serviceParameters := make(map[string]string)
	// 设置待检测图片URL.
	serviceParameters["imageUrl"] = imageURL

	serviceParametersBytes, _err := json.Marshal(serviceParameters)
	if _err != nil {
		return nil, _err
	}

	imageModerationRequest.SetService(serviceName.greenImageServiceName)
	imageModerationRequest.SetServiceParameters(string(serviceParametersBytes))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, err = greenClient.ImageModerationWithOptions(imageModerationRequest, runtime)
		if err != nil {
			return err
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, err = util.AssertAsString(error.Message)
		if err != nil {
			return nil, err
		}
	}

	return
}

// SubmitVoiceDetection 提交语音审核增强版(异步)
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/VoiceModeration
func SubmitVoiceDetection(voiceURL string) (response *green20220302.VoiceModerationResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}

	voiceModerationRequest := &green20220302.VoiceModerationRequest{}

	// 待检测语音URL.
	serviceParameters, _ := json.Marshal(
		map[string]interface{}{
			"url": voiceURL,
		},
	)
	voiceModerationRequest.SetService(serviceName.greenVoiceServiceName)
	voiceModerationRequest.SetServiceParameters(string(serviceParameters))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		_, err = greenClient.VoiceModerationWithOptions(voiceModerationRequest, runtime)
		if err != nil {
			return err
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, err = util.AssertAsString(error.Message)
		if err != nil {
			return
		}
	}

	return
}

// SearchVoiceDetectionResult 查询语音审核结果
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/VoiceModerationResult
func SearchVoiceDetectionResult(taskID string) (response *green20220302.VoiceModerationResultResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}

	voiceModerationResultRequest := &green20220302.VoiceModerationResultRequest{}

	serviceParameters, _ := json.Marshal(
		map[string]interface{}{
			"taskId": taskID,
		},
	)
	voiceModerationResultRequest.SetService(serviceName.greenVoiceServiceName)
	voiceModerationResultRequest.SetServiceParameters(string(serviceParameters))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, err = greenClient.VoiceModerationResultWithOptions(voiceModerationResultRequest, runtime)
		if err != nil {
			return
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, err = util.AssertAsString(error.Message)
		if err != nil {
			return
		}
	}

	return
}

// CancelVoiceDetectionTask 取消语音审核任务
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/VoiceModerationCancel
func CancelVoiceDetectionTask(taskID string) (response *green20220302.VoiceModerationCancelResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}

	voiceModerationCancelRequest := &green20220302.VoiceModerationCancelRequest{}

	serviceParameters, _ := json.Marshal(
		map[string]interface{}{
			"taskId": taskID,
		},
	)
	voiceModerationCancelRequest.SetService(serviceName.greenVoiceServiceName)
	voiceModerationCancelRequest.SetServiceParameters(string(serviceParameters))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, err = greenClient.VoiceModerationCancelWithOptions(voiceModerationCancelRequest, runtime)
		if err != nil {
			return
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, err = util.AssertAsString(error.Message)
		if err != nil {
			return
		}
	}

	return
}

// SubmitVideoDetection 视频审核增强版任务提交(异步)
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/VideoModeration
func SubmitVideoDetection(videoURL string) (response *green20220302.VideoModerationResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}

	videoModerationRequest := &green20220302.VideoModerationRequest{}

	serviceParameters, _ := json.Marshal(
		map[string]interface{}{
			"url": videoURL,
		},
	)
	videoModerationRequest.SetService(serviceName.greenVideoServiceName)
	videoModerationRequest.SetServiceParameters(string(serviceParameters))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, err = greenClient.VideoModerationWithOptions(videoModerationRequest, runtime)
		if err != nil {
			return
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, err = util.AssertAsString(error.Message)
		if err != nil {
			return
		}
	}

	return
}

// SearchVideoDetectionResult 查询视频审核结果
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/VideoModerationResult
func SearchVideoDetectionResult(taskID string) (response *green20220302.VideoModerationResultResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}

	videoModerationResultRequest := &green20220302.VideoModerationResultRequest{}

	serviceParameters, _ := json.Marshal(
		map[string]interface{}{
			"taskId": taskID,
		},
	)
	videoModerationResultRequest.SetService(serviceName.greenVideoServiceName)
	videoModerationResultRequest.SetServiceParameters(string(serviceParameters))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, err = greenClient.VideoModerationResultWithOptions(videoModerationResultRequest, runtime)
		if err != nil {
			return
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, err = util.AssertAsString(error.Message)
		if err != nil {
			return
		}
	}

	return
}

// CancelVideoDetectionTask 取消视频审核任务
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/VideoModerationCancel
func CancelVideoDetectionTask(taskID string) (response *green20220302.VideoModerationCancelResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}

	videoModerationCancelRequest := &green20220302.VideoModerationCancelRequest{}

	serviceParameters, _ := json.Marshal(
		map[string]interface{}{
			"taskId": taskID,
		},
	)
	videoModerationCancelRequest.SetService(serviceName.greenVideoServiceName)
	videoModerationCancelRequest.SetServiceParameters(string(serviceParameters))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, err = greenClient.VideoModerationCancelWithOptions(videoModerationCancelRequest, runtime)
		if err != nil {
			return err
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, err = util.AssertAsString(error.Message)
		if err != nil {
			return
		}
	}

	return
}

// SubmitFileDetection 文档检测任务提交(异步)
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/FileModeration
func SubmitFileDetection(fileURL string) (response *green20220302.FileModerationResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}

	fileModerationRequest := &green20220302.FileModerationRequest{}

	serviceParameters, _ := json.Marshal(
		map[string]interface{}{
			"url": fileURL,
		},
	)
	fileModerationRequest.SetService(serviceName.greenFileServiceName)
	fileModerationRequest.SetServiceParameters(string(serviceParameters))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, err = greenClient.FileModerationWithOptions(fileModerationRequest, runtime)
		if err != nil {
			return
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, err = util.AssertAsString(error.Message)
		if err != nil {
			return
		}
	}
	return
}

// SearchFileDetectionResult 查询文档检测结果
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/DescribeFileModerationResult
func SearchFileDetectionResult(taskID string) (response *green20220302.DescribeFileModerationResultResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}

	describeFileModerationResultRequest := &green20220302.DescribeFileModerationResultRequest{}

	serviceParameters, _ := json.Marshal(
		map[string]interface{}{
			"taskId": taskID,
		},
	)
	describeFileModerationResultRequest.SetService(serviceName.greenFileServiceName)
	describeFileModerationResultRequest.SetServiceParameters(string(serviceParameters))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, err = greenClient.DescribeFileModerationResultWithOptions(describeFileModerationResultRequest, runtime)
		if err != nil {
			return
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, err = util.AssertAsString(error.Message)
		if err != nil {
			return
		}
	}

	return
}

// SubmitURLDetection URL风险检测(异步)
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/UrlAsyncModeration
func SubmitURLDetection(URL string) (response *green20220302.UrlAsyncModerationResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}

	urlAsyncModerationRequest := &green20220302.UrlAsyncModerationRequest{}

	//构建图片检测请求。
	serviceParameters, _ := json.Marshal(
		map[string]interface{}{
			// 待检测url链接，公网可访问的URL。
			"url": URL,
		},
	)
	urlAsyncModerationRequest.SetService(serviceName.greenURLServiceName)
	urlAsyncModerationRequest.SetServiceParameters(string(serviceParameters))

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, err = greenClient.UrlAsyncModerationWithOptions(urlAsyncModerationRequest, runtime)
		if err != nil {
			return
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, err = util.AssertAsString(error.Message)
		if err != nil {
			return
		}
	}

	return
}

// SearchURLDetectionResult 查询URL异步检测结果
//
// Doc FYI(params of request or response) - https://next.api.aliyun.com/api/Green/2022-03-02/DescribeUrlModerationResult
func SearchURLDetectionResult(reqID string) (response *green20220302.DescribeUrlModerationResultResponse, err error) {
	if greenClient == nil {
		var once sync.Once
		once.Do(func() {
			_ = InitGreenClient()
		})
	}

	describeURLModerationResultRequest := &green20220302.DescribeUrlModerationResultRequest{
		ReqId: &reqID,
	}

	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		response, err = greenClient.DescribeUrlModerationResultWithOptions(describeURLModerationResultRequest, runtime)
		if err != nil {
			return
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
		// 错误 message
		fmt.Println(tea.StringValue(error.Message))
		// 诊断地址
		var data interface{}
		d := json.NewDecoder(strings.NewReader(tea.StringValue(error.Data)))
		d.Decode(&data)
		if m, ok := data.(map[string]interface{}); ok {
			recommend, _ := m["Recommend"]
			fmt.Println(recommend)
		}
		_, err = util.AssertAsString(error.Message)
		if err != nil {
			return
		}
	}

	return
}
