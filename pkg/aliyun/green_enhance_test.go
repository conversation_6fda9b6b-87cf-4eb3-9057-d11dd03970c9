package aliyun

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

// content response like below:
//
//	{
//		"code": 200,
//		"data": {
//		  "labels": "sexual_content,profanity",
//		  "reason": "{\"riskTips\":\"辱骂_严重辱骂,色情_低俗,辱骂_低俗\",\"riskWords\":\"操你妈,操你\"}"
//		},
//		"msg": "OK",
//		"requestId": "6E9751FF-B899-5265-B796-D9249C150E34"
//	 }
func TestTextSyncScanRequestEnhance(t *testing.T) {
	Convey("测试文本检测", t, func() {
		Convey("测试正常文本", func() {
			response, err := textDetection(`你好啊`)
			So(err, ShouldBeNil)
			So(response.Body.Data.Labels, ShouldNotBeNil)
			So(len(*response.Body.Data.Labels), ShouldEqual, 0) // 未发现违规
		})

		Convey("测试异常文本", func() {
			response, err := textDetection(`操你妈`)
			So(err, ShouldBeNil)
			So(response.Body.Data.Labels, ShouldNotBeNil)
			So(len(*response.Body.Data.Labels), ShouldNotEqual, 0) // 发现违规标签
		})
	})
}

// image response like below:
//
//	{
//		"headers": {
//		   "access-control-allow-origin": "*",
//		   "access-control-expose-headers": "*",
//		   "connection": "keep-alive",
//		   "content-length": "149",
//		   "content-type": "application/json;charset=utf-8",
//		   "date": "Wed, 27 Mar 2024 01:59:05 GMT",
//		   "etag": "17WHyPZaYo9WZXdpQLafq5A9",
//		   "keep-alive": "timeout=25",
//		   "x-acs-request-id": "D0F30F2E-2AB8-5E2D-8C88-727A27169B5E",
//		   "x-acs-trace-id": "30ba7c84707b905aec03e71861fcec7a"
//		},
//		"statusCode": 200,
//		"body": {
//		   "Code": 200,
//		   "Data": {
//			  "Result": [
//				 {
//					"Confidence": 98.82,
//					"Label": "sexual_maleTopless"
//				 }
//			  ]
//		   },
//		   "Msg": "success",
//		   "RequestId": "D0F30F2E-2AB8-5E2D-8C88-727A27169B5E"
//		}
//	 }
func TestImageSyncScanRequestEnhance(t *testing.T) {
	Convey("测试图片检测", t, func() {
		Convey("测试正常图片", func() {
			response, err := ImageDetection(`https://qiniu.other.cq-wnl.com/Fvzux74NTgTR5_VQcfCOm8n4i6A5`)
			So(err, ShouldBeNil)
			So(response.Body.Data.Result, ShouldNotBeNil)

			var lables []string
			for _, r := range response.Body.Data.Result {
				if r != nil && r.Label != nil && *r.Label != "nonLabel" { // 'nonLabel' 标签表示未出现违规
					lables = append(lables, *r.Label)
				}
			}
			So(len(lables), ShouldEqual, 0) // 未发现违规标签
		})

		Convey("测试异常图片", func() {
			response, err := ImageDetection(`https://qiniu.other.cq-wnl.com/FivBu8KKZ1CPPv71dA5tDC3ismZp`)
			So(err, ShouldBeNil)
			So(response.Body.Data.Result, ShouldNotBeNil)

			var lables []string
			for _, r := range response.Body.Data.Result {
				if r != nil && r.Label != nil && *r.Label != "nonLabel" { // 'nonLabel' 标签表示未出现违规
					lables = append(lables, *r.Label)
				}
			}
			So(len(lables), ShouldNotEqual, 0) // 发现违规标签
		})
	})
}
