package dingtalk

import (
	"chongli/component/apollo"
	"chongli/pkg/logger"
	"fmt"

	"github.com/blinkbean/dingtalk"
)

var dingTalk *dingtalk.DingTalk

func InitDingTalk() {
	dingToken := []string{apollo.GetApolloConfig().DingTalkRobotToken}
	dingTalk = dingtalk.InitDingTalk(dingToken, apollo.GetApolloConfig().DingTalkKey)
}

// SendDingTalk send ding talk message.
func SendDingTalk(code, phone, message string) {

	phones := []string{phone}
	_err := dingTalk.SendTextMessage(fmt.Sprintf("message:\r\n%v\r\n", message), dingtalk.WithAtMobiles(phones))
	if _err != nil {
		logger.Log().LogCode(code, fmt.Sprintf("send ding Talk error: [%v]", _err))
	}
	//if gin.Mode() == gin.DebugMode {
	logger.Log().LogCode(code, message)
	//}
}
