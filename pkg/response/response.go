package response

import (
	"chongli/pkg/aliyun"
	"chongli/pkg/common"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/manucorporat/try"
)

// gitVersion git version.
var gitVersion string

func init() {
	data, err := os.ReadFile("./.git_version.txt")
	if err != nil {
		return
	}
	gitVersion = string(data)
}

// SLSFlag enum WithSLSLog & WithNoSLSLog.
type SLSFlag = int

const (
	// WithSLSLog response with sls log.
	WithSLSLog SLSFlag = iota + 1
	// WithNoSLSLog response without sls log.
	WithNoSLSLog
)

// RespData response data.
type RespData struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Reason string `json:"reason,omitempty"`
	Data   any    `json:"data"`
	Meta   meta   `json:"meta"`
}
type meta struct {
	TraceId   string `json:"trace_id"`
	Timestamp int64  `json:"timestamp"`
}

// Response return response.
func Response(c *gin.Context, requestData any, responseData any, err errpkg.IError, slsConfig SLSFlag) {
	// set error to context.
	if err != nil {
		c.Set(`reason`, err.GetReason())
	}
	// with sls log.
	if slsConfig == WithSLSLog {
		common.Go(func() {
			sls(c, requestData, responseData)
		})
	}
	// response the client.
	response(c, responseData, err)
}

// response to client.
func response(c *gin.Context, responseData any, err errpkg.IError) {

	// success
	if err == nil {
		// 从context中提取traceId
		traceId := c.GetString("X-Request-Id")
		// 创建响应数据
		respData := &RespData{Code: http.StatusOK, Msg: "success", Data: responseData}
		// 如果有traceId，添加到响应的header中
		respData.Meta = meta{
			TraceId:   traceId,
			Timestamp: time.Now().UnixMilli(),
		}
		c.JSON(http.StatusOK, respData)
		return
	}
	// handle error response.
	responseErrHandle(c, err)
}

// responseErrHandle error response handle.
func responseErrHandle(c *gin.Context, err errpkg.IError) {
	code, exist := httpCode[err.GetErrorMsg()]
	if !exist {
		code = defaultHTTPCode[err.GetErrLevel()]
	}
	var responseData = RespData{
		Code: code,
		Msg:  err.GetErrorMsg(),
	}
	// 从context中提取traceId
	traceId := c.GetString("X-Request-Id")
	responseData.Meta = meta{
		TraceId:   traceId,
		Timestamp: time.Now().UnixMilli(),
	}
	// debug-mode will show the reason of error.
	if gin.Mode() != gin.ReleaseMode {
		responseData.Reason = fmt.Sprintf("%v", err.GetReason())
	}
	switch err.GetErrLevel() {
	case errpkg.ErrHighLevel:
		c.JSON(http.StatusInternalServerError, &responseData)
	case errpkg.ErrMiddleLevel, errpkg.ErrLowLevel:
		c.JSON(http.StatusOK, &responseData)
	default:
		c.JSON(http.StatusInternalServerError, &responseData)
	}
}

// Pong ping response. data is git version.
func Pong(c *gin.Context) {
	c.JSON(http.StatusOK, &RespData{
		Code: 0,
		Data: gitVersion,
		Msg:  "Pong",
	})
}

// SLS log.
func sls(c *gin.Context, input interface{}, output interface{}) {
	try.This(func() {
		uid, _ := c.Get("user_id")
		inputs := map[string]interface{}{
			"x-request-id":   c.GetString("X-Request-Id"),
			"x-request-time": c.GetString("X-Request-Time"),
			"host":           c.Request.Host,
			"request_method": c.Request.Method,
			"request_uri":    c.Request.URL,
			"request":        input,
			"user_id":        fmt.Sprintf("%v", uid),
			"ip":             c.ClientIP(),
		}
		in, _ := json.Marshal(inputs)
		req := string(in)
		out, _ := json.Marshal(output)
		outs := string(out)
		var sls aliyun.SLSConfig
		go sls.SetSLSConfig(c, req, outs).Send()
	}).Finally(func() {
		// Do nothing.
	}).Catch(func(e try.E) {
		logger.Log().Println(fmt.Sprintf("sls 日志发送失败: %v\r\n", e))
	})
}
