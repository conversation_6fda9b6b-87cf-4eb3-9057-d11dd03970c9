package stringchar

import (
	"crypto/md5"
	"encoding/hex"
)

// Reverse 翻转字符串.
func Reverse(str string) (strRev string) {
	n := len(str)
	runes := make([]rune, n)
	for _, r := range str {
		n--
		runes[n] = r
	}
	strRev = string(runes[n:])
	return
}

// Md5Hex 计算字符串的 MD5 散列值.
func Md5Hex(str []byte, length uint8) (md5Byte []byte) {
	h := md5.New()
	h.Write(str)
	hBytes := h.Sum(nil)
	dst := make([]byte, hex.EncodedLen(len(hBytes)))
	hex.Encode(dst, hBytes)
	if length > 0 && length < 32 {
		md5Byte = dst[:length]
		return
	}
	md5Byte = dst
	return
}
