package jwt

import (
	"fmt"
	"time"

	"github.com/dgrijalva/jwt-go"
)

const (
	ValidationErrorMalformed   = "token invalid."
	ValidationErrorExpired     = "token expired."
	ValidationErrorNotValidYet = "token not valid."
	ValidationErrorNotYetValid = "token illegality."
)

// JWT 定义一个jwt对象
type JWT struct {
	// 声明签名信息
	SigningKey []byte
}

// NewJWT 初始化jwt对象
func NewJWT(signingKey string) *JWT {
	return &JWT{
		[]byte(signingKey),
	}
}

// CustomClaims 自定义有效载荷(这里采用自定义的Name和Email作为有效载荷的一部分)
type CustomClaims struct {
	// 元数据, 保存所需字段信息
	MetaData map[string]interface{} `json:"meta_data"`
	// StandardClaims结构体实现了Claims接口(Valid()函数)
	jwt.StandardClaims
	// TODO 此处定义好 uid 放在 ctx里面去
}

// CreateToken 调用jwt-go库生成token
// 指定编码的算法为jwt.SigningMethodHS256
func (j *JWT) CreateToken(claims CustomClaims) (string, error) {
	// @link https://gowalker.org/github.com/dgrijalva/jwt-go#Token
	// 返回一个token的结构体指针
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.SigningKey)
}

// ParserToken token解码
func (j *JWT) ParserToken(tokenString string) (*CustomClaims, error) {
	// @link https://gowalker.org/github.com/dgrijalva/jwt-go#ParseWithClaims
	// 输入用户自定义的Claims结构体对象,token,以及自定义函数来解析token字符串为jwt的Token结构体指针
	// Keyfunc是匿名函数类型: type Keyfunc func(*Token) (iface{}, error)
	// func ParseWithClaims(tokenString string, claims Claims, keyFunc Keyfunc) (*Token, error) {}
	token, err := jwt.ParseWithClaims(tokenString, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		return j.SigningKey, nil
	})

	if err != nil {
		// @link https://gowalker.org/github.com/dgrijalva/jwt-go#ValidationError
		// jwt.ValidationError 是一个无效token的错误结构
		if ve, ok := err.(*jwt.ValidationError); ok {
			// ValidationErrorMalformed是一个uint常量，表示token不可用
			if ve.Errors&jwt.ValidationErrorMalformed != 0 {
				return nil, fmt.Errorf(ValidationErrorMalformed)
				// ValidationErrorExpired表示Token过期
			} else if ve.Errors&jwt.ValidationErrorExpired != 0 {
				return nil, fmt.Errorf(ValidationErrorExpired)
				// ValidationErrorNotValidYet表示无效token
			} else if ve.Errors&jwt.ValidationErrorNotValidYet != 0 {
				return nil, fmt.Errorf(ValidationErrorNotValidYet)
			} else {
				return nil, fmt.Errorf(ValidationErrorMalformed)
			}
		}
	}
	// 将token中的claims信息解析出来并断言成用户自定义的有效载荷结构
	if claims, ok := token.Claims.(*CustomClaims); ok && token.Valid {
		return claims, nil
	}
	return nil, fmt.Errorf(ValidationErrorNotYetValid)
}

// GenerateToken 生成token.
func GenerateToken(signingKey string, expiredTime time.Duration, meta map[string]interface{}) (token string, err error) {
	// 构造SignKey签名和解签名需要使用一个值
	create := NewJWT(signingKey)
	// 构造用户claims信息(负荷)
	claims := CustomClaims{
		MetaData: meta,
		StandardClaims: jwt.StandardClaims{
			NotBefore: time.Now().Unix() - 1000,                           // 签名生效时间
			ExpiresAt: time.Now().Unix() + int64(expiredTime/time.Second), // 签名过期时间
			Issuer:    signingKey,                                         // 签名颁发者
		},
	}
	// 根据claims生成token对象
	token, err = create.CreateToken(claims)
	return
}
