package errpkg

import (
	"errors"
	"fmt"
	"runtime"
)

// IError interfaces of error.
type IError interface {
	Error() string
	GetErrCause() error
	GetErrLevel() ErrorLevel
	GetErrorMsg() string
	GetReason() string
}

// Error error struct define.
type Error struct {
	errorLevel ErrorLevel
	errCause   error
	msg        string
	pc         uintptr
	fn         string
	line       int
}

// ErrorLevel error level type.
type ErrorLevel int

const (
	// ErrHighLevel high level error
	ErrHighLevel ErrorLevel = iota + 1
	// ErrMiddleLevel middle level error
	ErrMiddleLevel
	// ErrLowLevel low level error
	ErrLowLevel
)

// newError create an error.
func newError(errLevel ErrorLevel, errCause error, msg string, pc uintptr, fn string, line int) IError {
	if errCause == nil {
		errCause = errors.New("unknown error")
	}
	return &Error{
		errorLevel: errLevel,
		errCause:   errCause,
		msg:        msg,
		pc:         pc,
		fn:         fn,
		line:       line,
	}
}

// NewHighErrorWithCause create a high-level error with cause error.
func NewHighErrorWithCause(errCause error, msg string) IError {
	pc, fn, line, _ := runtime.Caller(1)
	return newError(ErrHighLevel, errCause, msg, pc, fn, line)
}

// NewHighError create a high-level error.
func NewHighError(msg string) IError {
	pc, fn, line, _ := runtime.Caller(1)
	return newError(ErrHighLevel, errors.New(msg), msg, pc, fn, line)
}

// NewMiddleErrorWithCause create a param-level error with cause error.
func NewMiddleErrorWithCause(errCause error, msg string) IError {
	pc, fn, line, _ := runtime.Caller(1)
	return newError(ErrMiddleLevel, errCause, msg, pc, fn, line)
}

// NewMiddleError create a middle-level error.
func NewMiddleError(msg string) IError {
	pc, fn, line, _ := runtime.Caller(1)
	return newError(ErrMiddleLevel, errors.New(msg), msg, pc, fn, line)
}

// NewLowErrorWithCause create a low-level error with cause error.
func NewLowErrorWithCause(errCause error, msg string) IError {
	pc, fn, line, _ := runtime.Caller(1)
	return newError(ErrLowLevel, errCause, msg, pc, fn, line)
}

// NewLowError create a low-level error.
func NewLowError(msg string) IError {
	pc, fn, line, _ := runtime.Caller(1)
	return newError(ErrLowLevel, errors.New(msg), msg, pc, fn, line)
}

// Error get the message of cause error .
func (e *Error) Error() string {
	return e.errCause.Error()
}

// GetErrCause get the cause error.
func (e *Error) GetErrCause() error {
	return e.errCause
}

// GetErrLevel get the error level.
func (e *Error) GetErrLevel() ErrorLevel {
	return e.errorLevel
}

// GetErrorMsg get the error message.
func (e *Error) GetErrorMsg() string {
	return e.msg
}

// GetReason get the error reason.
func (e *Error) GetReason() string {
	return fmt.Sprintf("[error] in %s; [%s:%d] reason for : [%v]", runtime.FuncForPC(e.pc).Name(), e.fn, e.line, e.Error())
}
