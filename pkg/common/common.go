package common

import (
	"chongli/component/apollo"
	"chongli/pkg/logger"
	"crypto/md5"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"math/rand"
	"net/url"
	"reflect"
	"regexp"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"unicode/utf8"
)

// SensitiveType IsSensitive 大小写是否敏感, Sensitive 1敏感, Sensitive 2不敏感
type SensitiveType int

const (
	Sensitive SensitiveType = iota + 1
	NotSensitive
)

// S2M struct to map.
func S2M(s interface{}) (m map[string]interface{}, err error) {
	var bytes []byte
	if bytes, err = json.Marshal(s); err != nil {
		return
	}
	err = json.Unmarshal(bytes, &m)
	return
}

// Copier copy a struct to another.
func Copier(to, from interface{}) (err error) {
	var bytes []byte

	if bytes, err = json.Marshal(from); err != nil {
		return errors.Wrap(err, "marshal from data err")
	}

	if err = json.Unmarshal(bytes, to); err != nil {
		return errors.Wrap(err, "unmarshal to data err")
	}

	return nil
}

// InArrayStrings 是否在字符数组内
func InArrayStrings(target string, array []string, sensitiveType SensitiveType) bool {
	sort.Strings(array)
	index := sort.SearchStrings(array, target)
	// index的取值:[0, len(str_array)-1]
	if index < len(array) {
		if sensitiveType == NotSensitive {
			return strings.EqualFold(array[index], target)
		}
		return array[index] == target
	}
	return false
}

// HttpBuildQuery http_build_query.
func HttpBuildQuery(m map[string]string) (query string) {
	var uri url.URL
	q := uri.Query()
	for k, v := range m {
		q.Add(k, v)
	}
	query = q.Encode()
	return
}

// ParseInt64 转int64
func ParseInt64(str string) (parse int64) {
	if str == "" {
		return
	}
	parse, _ = strconv.ParseInt(str, 10, 64)
	return
}

// ParseFloat64 转float64
func ParseFloat64(floats float64) (parse float64) {
	parse, _ = strconv.ParseFloat(fmt.Sprintf("%.4f", floats), 64)
	return
}

// ParseFloat64Decimal 保留floor位小数
func ParseFloat64Decimal(floats float64, floor int) (parse float64) {
	parse, _ = decimal.NewFromFloat(floats).RoundFloor(int32(floor)).Float64()
	return
}

func IsStruct(i interface{}) bool {
	eType := reflect.TypeOf(i)
	for eType.Kind() == reflect.Ptr {
		eType = eType.Elem()
	}
	return eType.Kind() == reflect.Struct
}

func HasField(obj interface{}, field string) bool {
	var ok bool
	eType := reflect.TypeOf(obj)
	for eType.Kind() == reflect.Ptr {
		eType = eType.Elem()
	}
	_, ok = eType.FieldByName(field)
	return ok
}

func SetFieldValue(obj interface{}, fieldName string, value interface{}) error {
	objValue := reflect.ValueOf(obj)
	if objValue.Kind() == reflect.Ptr {
		objValue = objValue.Elem()
	}

	fieldValue := objValue.FieldByName(fieldName)
	if !fieldValue.IsValid() {
		return fmt.Errorf("field %s not found", fieldName)
	}

	if !fieldValue.CanSet() {
		return fmt.Errorf("field %s cannot be set", fieldName)
	}

	inValue := reflect.ValueOf(value)
	if fieldValue.Type() != inValue.Type() {
		return fmt.Errorf("provided value type %s doesn't match field type %s", inValue.Type(), fieldValue.Type())
	}

	fieldValue.Set(inValue)
	return nil
}

// Go 安全启用goroutine
func Go(fn func()) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Log().LogCode("goroutine", fmt.Sprintf("调用go协程异常:[%v]", string(debug.Stack())))
			}
		}()
		fn()
	}()
}

// StructFieldNames 获取结构体中包含的字段名(取json tag)（不包含二级）
func StructFieldNames(s interface{}) []string {
	t := reflect.TypeOf(s)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	var fieldNames []string
	for i := 0; i < t.NumField(); i++ {
		fieldNames = append(fieldNames, t.Field(i).Tag.Get("json"))
	}
	return fieldNames
}

// SHA256 SHA256加密
func SHA256(str string) string {
	// 创建SHA256哈希对象
	hash := sha256.New()
	// 写入数据
	hash.Write([]byte(str))
	// 获取哈希值
	value := hash.Sum(nil)
	return string(value)
}

// GenValidateCode 生成随机数字验证码
func GenValidateCode(width int) string {
	numeric := [10]byte{0, 1, 2, 3, 4, 5, 6, 7, 8, 9}
	r := len(numeric)

	var sb strings.Builder
	for i := 0; i < width; i++ {
		_, _ = fmt.Fprintf(&sb, "%d", numeric[rand.Intn(r)])
	}
	return sb.String()
}

// ClearPlatform 清洗 platform 不管ios 安卓大写小写，或者不符合这个规范的，一律转换为小写，字母对不上的返回错误
func ClearPlatform(platform string) (string, error) {
	if platform == "Youloft_Android" {
		platform = "android"
	} else if platform == "Youloft_IOS" {
		platform = "ios"
	}
	platform = strings.ToLower(platform)
	if platform != "ios" && platform != "android" {
		return "", errors.New("platform must be ios or android")
	}
	return platform, nil
}

// GetUserId 获取登录用户的 userId
func GetUserId(c *gin.Context) int64 {
	userId, ok := c.Get("user_id")
	if !ok {
		return -1
	}
	return int64(userId.(float64))
}

// IsValidPhoneNumber 校验手机号码格式
func IsValidPhoneNumber(phone string) bool {
	// 定义正则表达式，匹配11位数字且以1开头
	pattern := `^1[3456789]\d{9}$`
	reg := regexp.MustCompile(pattern)
	return reg.MatchString(phone)
}

// SmsMD5 发送验证码之前的MD5加密获取签名
func SmsMD5(phone, timestamp string) string {
	return fmt.Sprintf("%x",
		md5.Sum([]byte("phone="+phone+"&"+"secret_key="+apollo.GetApolloConfig().PhoneCodeSecret+"&"+"timestamp="+timestamp)))
}

// CheckTextLength 校验文本长度是否不在 5~500
func CheckTextLength(text string) bool {
	if utf8.RuneCountInString(text) <= 5 || utf8.RuneCountInString(text) >= 500 {
		return true
	}
	return false
}
