package dto

import (
	"time"
)

// GuiyinDeviceBindDto 设备归因绑定DTO - 业务内部流转使用
type GuiyinDeviceBindDto struct {
	Id        int                 `json:"id"`         // 主键ID
	DeviceId  string              `json:"device_id"`  // 设备ID
	AllData   string              `json:"all_data"`   // 全部数据
	ChannelId int                 `json:"channel_id"` // 渠道ID
	CreateAt  time.Time           `json:"create_at"`  // 创建时间
	UpdateAt  time.Time           `json:"update_at"`  // 更新时间
	Channel   MarketingChannelDto `json:"channel"`    // 渠道
}

// GuiyinDeviceBindCreateDto 创建设备绑定DTO - 业务内部流转使用
type GuiyinDeviceBindCreateDto struct {
	DeviceId  string `json:"device_id"`  // 设备ID
	AllData   string `json:"all_data"`   // 全部数据
	ChannelId int    `json:"channel_id"` // 渠道ID
}

// GuiyinDeviceBindUpdateDto 更新设备绑定DTO - 业务内部流转使用
type GuiyinDeviceBindUpdateDto struct {
	Id        int    `json:"id"`         // 主键ID
	DeviceId  string `json:"device_id"`  // 设备ID
	AllData   string `json:"all_data"`   // 全部数据
	ChannelId int    `json:"channel_id"` // 渠道ID
}

// GuiyinDeviceBindDeleteDto 删除设备绑定DTO - 业务内部流转使用
type GuiyinDeviceBindDeleteDto struct {
	Id int `json:"id"` // 主键ID
}
