package dto

import "mime/multipart"

// UploadRequestDto 上传请求DTO
type UploadRequestDto struct {
	File *multipart.FileHeader `json:"file" form:"file" binding:"required"`
}

type UploadMultiRequestDto struct {
	Files    []*multipart.FileHeader `json:"files" form:"files" binding:"required"`
	FileName string                  `form:"fileName" json:"fileName"`
}

// UploadResponseDto 上传响应DTO
type UploadResponseDto struct {
	URL string `json:"url"`
}
