package dto

import (
	"chongli/internal/model"
	"time"
)

// ConfigDto 配置DTO - 业务内部流转使用
type ConfigDto struct {
	Id          int64            `json:"id"`           // 主键id
	ConfigKey   string           `json:"config_key"`   // 配置名
	ConfigValue string           `json:"config_value"` // 配置值
	Remark      string           `json:"remark"`       // 备注信息
	CreateAt    time.Time        `json:"create_at"`    // 创建时间
	IsDelete    model.StatusFlag `json:"is_delete"`    // 删除标志
	IsChannel   model.StatusFlag `json:"is_channel"`   // 是否是渠道配置
	IsPublish   model.StatusFlag `json:"is_publish"`   // 是否发布
}

// ConfigCreateDto 创建配置DTO - 业务内部流转使用
type ConfigCreateDto struct {
	ConfigKey   string `json:"config_key"`   // 配置名
	ConfigValue string `json:"config_value"` // 配置值
	Remark      string `json:"remark"`       // 备注信息
	IsChannel   int8   `json:"is_channel"`   // 是否是渠道配置
	IsDelete    int8   `json:"is_delete"`    // 删除标志
	IsPublish   int8   `json:"is_publish"`   // 是否发布
}

// ConfigUpdateDto 更新配置DTO - 业务内部流转使用
type ConfigUpdateDto struct {
	Id          int64            `json:"id"`           // 主键id
	ConfigKey   string           `json:"config_key"`   // 配置名
	ConfigValue string           `json:"config_value"` // 配置值
	Remark      string           `json:"remark"`       // 备注信息
	IsChannel   model.StatusFlag `json:"is_channel"`   // 是否是渠道配置
	IsDelete    model.StatusFlag `json:"is_delete"`    // 删除标志
	IsPublish   model.StatusFlag `json:"is_publish"`   // 是否发布
}

// ConfigQueryDto 查询配置DTO - 业务内部流转使用
type ConfigQueryDto struct {
	Page      int              `json:"page"`       // 页码
	PageSize  int              `json:"page_size"`  // 每页大小
	Id        int64            `json:"id"`         // 主键id
	ConfigKey string           `json:"config_key"` // 配置名
	IsDelete  model.StatusFlag `json:"is_delete"`  // 删除标志
	IsChannel model.StatusFlag `json:"is_channel"` // 是否是渠道配置
	IsPublish model.StatusFlag `json:"is_publish"` // 是否发布
}

// ConfigDeleteDto 删除配置DTO - 业务内部流转使用
type ConfigDeleteDto struct {
	Id int64 `json:"id"` // 主键id
}

// ConfigChannelRelationDto 配置渠道关联DTO
type ConfigChannelRelationDto struct {
	ID                 int64                   `json:"id"`                          // 主键ID
	ConfigID           int                     `json:"config_id"`                   // 配置ID
	MarketingChannelID int                     `json:"marketing_channel_id"`        // 营销渠道ID
	CreateAt           time.Time               `json:"create_at"`                   // 创建时间
	Config             *ConfigDto              `json:"config,omitempty"`            // 关联的配置信息
	MarketingChannel   *model.MarketingChannel `json:"marketing_channel,omitempty"` // 关联的营销渠道信息
	Page               int                     `json:"page"`                        // 页码
	PageSize           int                     `json:"page_size"`                   // 每页大小
}

// ConfigChannelRelationQueryDto 配置渠道关联查询DTO - 业务内部流转使用
type ConfigChannelRelationQueryDto struct {
	Page               int `json:"page"`                 // 页码
	PageSize           int `json:"page_size"`            // 每页大小
	ConfigID           int `json:"config_id"`            // 配置ID
	MarketingChannelID int `json:"marketing_channel_id"` // 营销渠道ID
}

// ConfigBindChannelDto 配置绑定渠道DTO - 业务内部流转使用
type ConfigBindChannelDto struct {
	ConfigID           int   `json:"config_id"`            // 配置ID
	MarketingChannelID []int `json:"marketing_channel_id"` // 营销渠道ID列表
}

// VideoConfigDto 视频配置DTO
type VideoConfigDto struct {
	CreateSpend int `json:"create_spend"` // 创建花费
}

type DefaultVipGiveDiamondDto struct {
	MonthGive   uint64 `json:"month_monthly_give"`
	QuarterGive uint64 `json:"quarter_monthly_give"`
	YearGive    uint64 `json:"year_monthly_give"`
}

// VipTestModeDto VIP测试模式配置
type VipTestModeDto struct {
	IsTestMode bool `json:"is_test_mode"` // 是否启用测试模式：true=每小时赠送，false=每30天赠送
}

type AppMainClassConfigDto struct {
	MainClassName string `json:"main_class_name"`
	MainClassId   int64  `json:"main_class_id"` // 主类ID
}
