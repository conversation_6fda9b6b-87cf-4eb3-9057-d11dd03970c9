package dto

import "time"

// PayOrderDto 支付订单数据传输对象
type PayOrderDto struct {
	ID                  int64      `json:"id"`                     // 主键id
	OrderID             string     `json:"order_id"`               // 订单id
	WnlOrderID          string     `json:"wnl_order_id"`           // 万年历支付成功订单id
	OrderState          int        `json:"order_state"`            // 订单状态，-1：待支付；1：已支付；2：已过期；3：已退款
	OrderStateText      string     `json:"order_state_text"`       // 订单状态文本
	GoodsID             int64      `json:"goods_id"`               // 商品主键id
	GoodsMiddleID       string     `json:"goods_middle_id"`        // 商品中台id
	GoodsTitle          string     `json:"goods_title"`            // 商品名称
	UserID              int64      `json:"user_id"`                // 用户id
	UserDeviceID        string     `json:"user_device_id"`         // 用户设备id
	UserBindChannelID   int64      `json:"user_bind_channel_id"`   // 用户绑定渠道id
	UserBindChannelName string     `json:"user_bind_channel_name"` // 用户绑定渠道名称
	UserIP              string     `json:"user_ip"`                // 用户ip
	PayType             int        `json:"pay_type"`               // 支付方式，1：微信；2：支付宝；3：苹果支付；4：沙盒支付
	PayTypeText         string     `json:"pay_type_text"`          // 支付方式文本
	OrderAmount         float64    `json:"order_amount"`           // 订单应付金额
	PayAmount           float64    `json:"pay_amount"`             // 实际支付金额
	RefundAmount        float64    `json:"refund_amount"`          // 退款金额
	Version             string     `json:"version"`                // app版本
	Channel             string     `json:"channel"`                // app渠道
	PayAt               *time.Time `json:"pay_at"`                 // 支付时间
	RefundAt            *time.Time `json:"refund_at"`              // 退款时间
	ExpireAt            *time.Time `json:"expire_at"`              // 订单过期时间
	CreateAt            time.Time  `json:"create_at"`              // 创建时间
	UpdateAt            time.Time  `json:"update_at"`              // 更新时间
	WnlCallbackData     string     `json:"wnl_callback_data"`      // 万年历支付回调数据
	Remark              string     `json:"remark"`                 // 订单备注
	IsDelete            int8       `json:"is_delete"`              // 是否已被删除，-1：未删除；1：已删除
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	GoodsID      int64  `json:"goods_id" form:"goods_id" binding:"required"` // 商品主键id
	UserId       int64  `json:"user_id"`                                     // 用户id
	UserDeviceId string `json:"user_device_id"`                              // 用户设备id
	UserIP       string `json:"user_ip"`                                     // 用户ip
	Version      string `json:"version"`                                     // app版本
	Channel      string `json:"channel"`                                     // app渠道
}

// CreateOrderResponse 创建订单响应
type CreateOrderResponse struct {
	OrderID       string `json:"order_id"`
	GoodsMiddleID string `json:"goods_middle_id"`
	MchCode       string `json:"mch_code"`
	PartnerId     string `json:"partner_id"`
}

// GetPayOrderListRequest 获取支付订单列表请求
type GetPayOrderListRequest struct {
	Page                int        `json:"page" form:"page" binding:"required,min=1"`                   // 页码
	PageSize            int        `json:"page_size" form:"page_size" binding:"required,min=1,max=100"` // 每页数量
	OrderID             string     `json:"order_id" form:"order_id"`                                    // 订单id
	WnlOrderID          string     `json:"wnl_order_id" form:"wnl_order_id"`                            // 万年历支付成功订单id
	OrderState          *int       `json:"order_state" form:"order_state"`                              // 订单状态
	GoodsID             int64      `json:"goods_id" form:"goods_id"`                                    // 商品主键id
	GoodsMiddleID       string     `json:"goods_middle_id" form:"goods_middle_id"`                      // 商品中台id
	UserID              *int64     `json:"user_id" form:"user_id"`                                      // 用户id
	UserDeviceID        string     `json:"user_device_id" form:"user_device_id"`                        // 用户设备id
	UserBindChannelID   int64      `json:"user_bind_channel_id" form:"user_bind_channel_id"`            // 用户绑定渠道id
	UserBindChannelName string     `json:"user_bind_channel_name" form:"user_bind_channel_name"`        // 用户绑定渠道名称
	UserIP              string     `json:"user_ip" form:"user_ip"`                                      // 用户ip
	PayType             *int       `json:"pay_type" form:"pay_type"`                                    // 支付方式
	Version             string     `json:"version" form:"version"`                                      // app版本
	Channel             string     `json:"channel" form:"channel"`                                      // app渠道
	IsDelete            *int8      `json:"is_delete" form:"is_delete"`                                  // 是否已被删除
	BeginCreateAt       *time.Time `json:"begin_create_at" form:"begin_create_at"`                      // 创建时间起始点
	EndCreateAt         *time.Time `json:"end_create_at" form:"end_create_at"`                          // 创建时间结束点
	BeginPayAt          *time.Time `json:"begin_pay_at" form:"begin_pay_at"`                            // 支付时间起始点
	EndPayAt            *time.Time `json:"end_pay_at" form:"end_pay_at"`                                // 支付时间结束点
}

// GetPayOrderListResponse 获取支付订单列表响应
type GetPayOrderListResponse struct {
	Total int64          `json:"total"`
	List  []*PayOrderDto `json:"list"`
}

// UpdatePayOrderRequest 更新支付订单请求
type UpdatePayOrderRequest struct {
	ID              int64      `json:"id" binding:"required"`                          // 主键id
	WnlOrderID      string     `json:"wnl_order_id"`                                   // 万年历支付成功订单id
	OrderState      *int       `json:"order_state" binding:"omitempty,oneof=-1 1 2 3"` // 订单状态
	GoodsTitle      string     `json:"goods_title"`                                    // 商品名称
	PayType         *int       `json:"pay_type" binding:"omitempty,oneof=1 2 3 4"`     // 支付方式
	OrderAmount     *float64   `json:"order_amount"`                                   // 订单应付金额
	PayAmount       *float64   `json:"pay_amount"`                                     // 实际支付金额
	RefundAmount    *float64   `json:"refund_amount"`                                  // 退款金额
	PayAt           *time.Time `json:"pay_at"`                                         // 支付时间
	RefundAt        *time.Time `json:"refund_at"`                                      // 退款时间
	ExpireAt        *time.Time `json:"expire_at"`                                      // 订单过期时间
	WnlCallbackData string     `json:"wnl_callback_data"`                              // 万年历支付回调数据
	Remark          string     `json:"remark"`                                         // 订单备注
	IsDelete        *int8      `json:"is_delete" binding:"omitempty,oneof=-1 1"`       // 是否已被删除
}

// PayOrderDetailResponse 支付订单详情响应
type PayOrderDetailResponse struct {
	*PayOrderDto
}

// OrderStatisticsDto 订单统计数据DTO
type OrderStatisticsDto struct {
	TotalOrders                     int64                   `json:"total_orders"`                          // 总订单量
	TotalOrdersByChannel            []ChannelOrderCountDto  `json:"total_orders_by_channel"`               // 各渠道总订单量
	TotalPaidAmount                 float64                 `json:"total_paid_amount"`                     // 总已支付金额（排除沙盒）
	TotalPaidAmountByChannel        []ChannelOrderAmountDto `json:"total_paid_amount_by_channel"`          // 各渠道总已支付金额
	TotalRefundAmount               float64                 `json:"total_refund_amount"`                   // 总退款金额（排除沙盒）
	TotalRefundAmountByChannel      []ChannelOrderAmountDto `json:"total_refund_amount_by_channel"`        // 各渠道总退款金额
	TotalPaidOrders                 int64                   `json:"total_paid_orders"`                     // 总已支付订单量（排除沙盒）
	TotalPaidOrdersByChannel        []ChannelOrderCountDto  `json:"total_paid_orders_by_channel"`          // 各渠道总已支付订单量
	MonthlyPaidOrderStats           []MonthlyOrderStatsDto  `json:"monthly_paid_order_stats"`              // 每月已支付订单统计
	YesterdayNewOrders              int64                   `json:"yesterday_new_orders"`                  // 昨日新增订单量
	YesterdayNewOrdersByChannel     []ChannelOrderCountDto  `json:"yesterday_new_orders_by_channel"`       // 昨日各渠道新增订单
	YesterdayNewPaidOrders          int64                   `json:"yesterday_new_paid_orders"`             // 昨日新增已支付订单量（排除沙盒）
	YesterdayNewPaidOrdersByChannel []ChannelOrderCountDto  `json:"yesterday_new_paid_orders_by_channel"`  // 昨日各渠道新增已支付订单
	ThisMonthNewOrders              int64                   `json:"this_month_new_orders"`                 // 本月新增订单量
	ThisMonthNewOrdersByChannel     []ChannelOrderCountDto  `json:"this_month_new_orders_by_channel"`      // 本月各渠道新增订单
	ThisMonthNewPaidOrders          int64                   `json:"this_month_new_paid_orders"`            // 本月新增已支付订单量（排除沙盒）
	ThisMonthNewPaidOrdersByChannel []ChannelOrderCountDto  `json:"this_month_new_paid_orders_by_channel"` // 本月各渠道新增已支付订单
	TodayNewOrders                  int64                   `json:"today_new_orders"`                      // 本日新增订单量
	TodayNewOrdersByChannel         []ChannelOrderCountDto  `json:"today_new_orders_by_channel"`           // 本日各渠道新增订单
	TodayNewPaidOrders              int64                   `json:"today_new_paid_orders"`                 // 本日新增已支付订单量（排除沙盒）
	TodayNewPaidOrdersByChannel     []ChannelOrderCountDto  `json:"today_new_paid_orders_by_channel"`      // 本日各渠道新增已支付订单
	ThisMonthDailyNewPaidOrders     []DailyOrderStatsDto    `json:"this_month_daily_new_paid_orders"`      // 本月每日新增已支付订单统计
}

// ChannelOrderCountDto 渠道订单数量DTO
type ChannelOrderCountDto struct {
	ChannelID   int64  `json:"channel_id"`   // 渠道ID
	ChannelName string `json:"channel_name"` // 渠道名称
	OrderCount  int64  `json:"order_count"`  // 订单数量
}

// ChannelOrderAmountDto 渠道订单金额DTO
type ChannelOrderAmountDto struct {
	ChannelID   int64   `json:"channel_id"`   // 渠道ID
	ChannelName string  `json:"channel_name"` // 渠道名称
	Amount      float64 `json:"amount"`       // 金额
}

// MonthlyOrderStatsDto 月度订单统计DTO
type MonthlyOrderStatsDto struct {
	Year                int                     `json:"year"`                   // 年份
	Month               int                     `json:"month"`                  // 月份
	TotalPaidOrders     int64                   `json:"total_paid_orders"`      // 总已支付订单量
	TotalPaidAmount     float64                 `json:"total_paid_amount"`      // 总已支付金额
	PaidOrdersByChannel []ChannelOrderCountDto  `json:"paid_orders_by_channel"` // 各渠道已支付订单量
	PaidAmountByChannel []ChannelOrderAmountDto `json:"paid_amount_by_channel"` // 各渠道已支付金额
}

// DailyOrderStatsDto 每日订单统计DTO
type DailyOrderStatsDto struct {
	Year                   int                    `json:"year"`                       // 年份
	Month                  int                    `json:"month"`                      // 月份
	Day                    int                    `json:"day"`                        // 日期
	TotalNewPaidOrders     int64                  `json:"total_new_paid_orders"`      // 总新增已支付订单量
	NewPaidOrdersByChannel []ChannelOrderCountDto `json:"new_paid_orders_by_channel"` // 各渠道新增已支付订单
}

// PayNotifyDto 支付回调DTO
type PayNotifyDto struct {
	WnlOrderId    string `form:"orderId"`    // 万年历支付成功订单id
	GoodsMiddleId string `form:"goodsID"`    // 商品中台id
	OrderId       string `form:"mhtOrderNo"` // App订单id
	Fee           string `form:"fee"`        // 金额
	PayType       int8   `form:"payType"`    // 0：支付宝网页 1：微信 2：支付宝客户端 3：苹果支付 4：微信H5支付
	ExtraData     string `form:"extraData"`
	Token         string `form:"token"` // 签名验证参数，生产环境需要验证签名
	// 非必需参数
	NotifyType *string `form:"notifyType"` // REFUND 为退款
	Key        *int    `form:"key"`        // 苹果支付时才会传递来的参数, 0:正式;1:沙盒
}
