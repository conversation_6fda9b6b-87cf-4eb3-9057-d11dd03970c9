package dto

import (
	"chongli/internal/model"
	"time"
)

// MarketingChannelDto 营销渠道配置DTO
type MarketingChannelDto struct {
	ID        int       `json:"id"`         // 主键id
	Type      int8      `json:"type"`       // 类型：1-匹配商店渠道，2-匹配归因推广
	Title     string    `json:"title"`      // 标题
	BindKey   string    `json:"bind_key"`   // 绑定键
	BindValue string    `json:"bind_value"` // 绑定值
	IsDeleted int8      `json:"is_deleted"` // 是否删除：-1-未删除，1-已删除
	CreateAt  time.Time `json:"create_at"`  // 创建时间
	UpdateAt  time.Time `json:"update_at"`  // 更新时间
}

// MarketingChannelCreateDto 创建营销渠道DTO
type MarketingChannelCreateDto struct {
	Type      int8             `json:"type" binding:"required,oneof=1 2"`     // 类型：1-匹配商店渠道，2-匹配归因推广
	Title     string           `json:"title"`                                 // 标题
	BindKey   string           `json:"bind_key" binding:"required,max=128"`   // 绑定键
	BindValue string           `json:"bind_value" binding:"required,max=255"` // 绑定值
	IsDeleted model.StatusFlag `json:"is_deleted" binding:"oneof=-1 1"`       // 是否删除：-1-未删除，1-已删除
}

// MarketingChannelUpdateDto 更新营销渠道DTO
type MarketingChannelUpdateDto struct {
	ID        int              `json:"id" binding:"required,min=1"`           // 主键id
	Type      int8             `json:"type" binding:"required,oneof=1 2"`     // 类型：1-匹配商店渠道，2-匹配归因推广
	Title     string           `json:"title"`                                 // 标题
	BindKey   string           `json:"bind_key" binding:"required,max=128"`   // 绑定键
	BindValue string           `json:"bind_value" binding:"required,max=255"` // 绑定值
	IsDeleted model.StatusFlag `json:"is_deleted" binding:"oneof=-1 1"`       // 是否删除：-1-未删除，1-已删除
}

// MarketingChannelQueryDto 查询营销渠道DTO
type MarketingChannelQueryDto struct {
	Page      int    `json:"page" form:"page" binding:"min=1"`                   // 页码
	PageSize  int    `json:"page_size" form:"page_size" binding:"min=1,max=100"` // 每页大小
	Type      *int8  `json:"type" form:"type"`                                   // 类型筛选
	Title     string `json:"title" form:"title"`                                 // 标题筛选
	BindKey   string `json:"bind_key" form:"bind_key"`                           // 绑定键筛选
	BindValue string `json:"bind_value" form:"bind_value"`                       // 绑定值筛选
	IsDeleted *int8  `json:"is_delete" form:"is_delete"`                         // 删除状态筛选
}
