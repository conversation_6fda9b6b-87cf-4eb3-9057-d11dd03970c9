package dto

import (
	"chongli/internal/model"
	"time"
)

// PopupDto 弹窗DTO
type PopupDto struct {
	ID         int       `json:"id"`          // 主键ID
	Location   string    `json:"location"`    // 弹窗位置
	Jump       string    `json:"jump"`        // 跳转类型
	JumpParam  string    `json:"jump_param"`  // 跳转参数
	PopupImg   string    `json:"popup_img"`   // 弹窗图片
	PopupVideo string    `json:"popup_video"` // 弹窗视频
	Title      string    `json:"title"`       // 标题
	Content    string    `json:"content"`     // 内容
	IsDelete   int8      `json:"is_delete"`   // 删除标志
	CreateAt   time.Time `json:"create_at"`   // 创建时间
	UpdateAt   time.Time `json:"update_at"`   // 更新时间
}

// PopupCreateDto 创建弹窗DTO
type PopupCreateDto struct {
	Location   string `json:"location" binding:"required,max=32"`    // 弹窗位置
	Jump       string `json:"jump" binding:"required,max=128"`       // 跳转类型
	JumpParam  string `json:"jump_param" binding:"required,max=255"` // 跳转参数
	PopupImg   string `json:"popup_img" binding:"max=255"`           // 弹窗图片
	PopupVideo string `json:"popup_video" binding:"max=255"`         // 弹窗视频
	Title      string `json:"title" binding:"max=128"`               // 标题
	Content    string `json:"content" binding:"max=255"`             // 内容
	IsDelete   int8   `json:"is_delete" binding:"oneof=-1 1"`        // 删除标志
}

// PopupUpdateDto 更新弹窗DTO
type PopupUpdateDto struct {
	ID         int              `json:"id" binding:"required,min=1"`           // 主键ID
	Location   string           `json:"location" binding:"required,max=32"`    // 弹窗位置
	Jump       string           `json:"jump" binding:"required,max=128"`       // 跳转类型
	JumpParam  string           `json:"jump_param" binding:"required,max=255"` // 跳转参数
	PopupImg   string           `json:"popup_img" binding:"max=255"`           // 弹窗图片
	PopupVideo string           `json:"popup_video" binding:"max=255"`         // 弹窗视频
	Title      string           `json:"title" binding:"max=128"`               // 标题
	Content    string           `json:"content" binding:"max=255"`             // 内容
	IsDelete   model.StatusFlag `json:"is_delete" binding:"oneof=-1 1"`        // 删除标志
}

// PopupQueryDto 查询弹窗DTO
type PopupQueryDto struct {
	Page     int              `json:"page" form:"page" binding:"min=1"`                   // 页码
	PageSize int              `json:"page_size" form:"page_size" binding:"min=1,max=100"` // 每页大小
	ID       int              `json:"id" form:"id"`                                       // 主键ID
	Location string           `json:"location" form:"location"`                           // 弹窗位置
	Title    string           `json:"title" form:"title"`                                 // 标题
	IsDelete model.StatusFlag `json:"is_delete" form:"is_delete"`                         // 删除标志
}

// PopupDeleteDto 删除弹窗DTO
type PopupDeleteDto struct {
	ID int `json:"id" binding:"required,min=1"` // 主键ID
}
