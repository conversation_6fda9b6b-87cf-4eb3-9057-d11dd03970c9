package dto

import "time"

// UserVipDto 用户VIP信息数据传输对象
type UserVipDto struct {
	ID         int64      `json:"id"`           // 主键id
	UserId     int64      `json:"user_id"`      // 用户id
	VipType    int8       `json:"vip_type"`     // 会员类型：1-月卡；2-季卡；3-年卡
	CreateAt   time.Time  `json:"create_at"`    // 开通时间
	RenewalAt  *time.Time `json:"renewal_at"`   // 续费时间
	ExpireAt   time.Time  `json:"expire_at"`    // 到期时间
	IsExpire   int8       `json:"is_expire"`    // 是否已过期
	IsDelete   int8       `json:"is_delete"`    // 是否删除：-1-未删除；1-已删除
	OldVipInfo string     `json:"old_vip_info"` // 旧会员信息，续费时使用
}

type UserVipInfo struct {
	VipType   int8       `json:"vip_type,omitempty"`   // 会员类型：1-月卡；2-季卡；3-年卡
	CreateAt  time.Time  `json:"create_at,omitempty"`  // 开通时间
	RenewalAt *time.Time `json:"renewal_at,omitempty"` // 续费时间
	ExpireAt  time.Time  `json:"expire_at,omitempty"`  // 到期时间
	IsExpire  int8       `json:"is_expire,omitempty"`  // 是否已过期
}

// UserVipGiveDto 会员赠送信息数据传输对象
type UserVipGiveDto struct {
	ID       int64     `json:"id"`        // 主键id
	UserId   int64     `json:"user_id"`   // 用户id
	GoodsId  int64     `json:"goods_id"`  // 来源商品ID
	OrderId  string    `json:"order_id"`  // 来源订单ID
	VipType  int8      `json:"vip_type"`  // 会员类型：1-月卡；2-季卡；3-年卡
	GiveAt   time.Time `json:"give_at"`   // 赠送时间
	GiveNum  int64     `json:"give_num"`  // 赠送钻石数量
	IsGive   int8      `json:"is_give"`   // 是否已赠送：-1-未赠送；1-已赠送
	IsDelete int8      `json:"is_delete"` // 是否删除：-1-未删除；1-已删除
	CreateAt time.Time `json:"create_at"` // 创建时间
}

// CreateAndRenewUserVipRequest 开通/续费VIP请求
type CreateAndRenewUserVipRequest struct {
	UserId    int64  `json:"user_id" binding:"required"`  // 用户id
	VipType   int8   `json:"vip_type" binding:"required"` // 会员类型：1-月卡；2-季卡；3-年卡
	Diamond   uint64 `json:"monthly_diamond"`             // 每月赠送钻石数量
	GiveCount int64  `json:"give_count"`                  // 赠送次数(同时也是会员月份时长)
	Version   string `json:"version"`                     // 版本
	Channel   string `json:"channel"`                     // 渠道
	OrderID   string `json:"order_id"`                    // 订单号
	GoodsID   int64  `json:"goods_id"`                    // 商品ID
}

// CreateVipGiveRequest 创建VIP赠送记录请求
type CreateVipGiveRequest struct {
	UserId  int64     `json:"user_id" binding:"required"`  // 用户id
	GoodsId int64     `json:"goods_id" binding:"required"` // 来源商品ID
	OrderId string    `json:"order_id" binding:"required"` // 来源订单ID
	VipType int8      `json:"vip_type" binding:"required"` // 会员类型：1-月卡；2-季卡；3-年卡
	GiveNum int64     `json:"give_num" binding:"required"` // 赠送钻石数量
	GiveAt  time.Time `json:"give_at"`                     // 赠送时间
}

// VipGivePageRequest VIP赠送记录分页查询请求
type VipGivePageRequest struct {
	Page     int       `json:"page" form:"page" binding:"min=1"`           // 页码
	PageSize int       `json:"page_size" form:"page_size" binding:"min=1"` // 每页大小
	UserId   int64     `json:"user_id" form:"user_id"`                     // 用户id
	GoodsId  int64     `json:"goods_id" form:"goods_id"`                   // 来源商品ID
	OrderId  string    `json:"order_id" form:"order_id"`                   // 来源
	VipType  int8      `json:"vip_type" form:"vip_type"`                   // 会员类型：1-月卡；2-季卡；3-年卡
	GiveNum  int64     `json:"give_num" form:"give_num"`                   // 赠送钻石数量
	IsGive   int8      `json:"is_give" form:"is_give"`                     // 是否已赠送
	IsDelete int8      `json:"is_delete" form:"is_delete"`                 // 是否删除
	BeginAt  time.Time `json:"begin_at" form:"begin_at"`                   // 开始赠送时间
	EndAt    time.Time `json:"end_at" form:"end_at"`                       // 结束赠送时间
}

// VipGivePageResponse VIP赠送记录分页查询响应
type VipGivePageResponse struct {
	Total int64             `json:"total"` // 总数
	List  []*UserVipGiveDto `json:"list"`  // 列表
}
