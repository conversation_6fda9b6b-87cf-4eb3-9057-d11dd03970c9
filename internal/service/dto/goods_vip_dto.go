package dto

import (
	"chongli/internal/model"
	"time"
)

// VipGoodsInfoDto VIP商品信息DTO
type VipGoodsInfoDto struct {
	Id             int              `json:"id" form:"id"`               // 主键id
	GoodsId        string           `json:"goods_id"`                   // 商品id
	Title          string           `json:"title"`                      // 商品名称
	Price          float64          `json:"price"`                      // 商品价格
	VipType        int              `json:"vip_type"`                   // vip类型;1:月;2:季;3:年
	Version        string           `json:"version"`                    // 创建版本
	VersionInt     int              `json:"version_int"`                // 版本int值
	Channel        string           `json:"channel"`                    // 客户端/渠道
	Sort           int              `json:"sort"`                       // 排序;升序排列
	MonthlyDiamond int              `json:"monthly_diamond"`            // 月钻石数量
	GiveCount      int              `json:"give_count"`                 // 赠送次数
	IsDisplay      model.StatusFlag `json:"is_display"`                 // 是否显示，-1：否；1：是
	IsDelete       int8             `json:"is_delete" form:"is_delete"` // 是否已被删除，0：未删除；1：已删除
	CreatedAt      string           `json:"created_at"`                 // 创建时间
	UpdatedAt      string           `json:"updated_at"`                 // 更新时间

	// 查询参数
	Page      int    `json:"page" form:"page"`             // 页码
	Size      int    `json:"size" form:"size"`             // 每页大小
	OrderBy   string `json:"order_by" form:"order_by"`     // 排序字段
	IsChannel int    `json:"is_channel" form:"is_channel"` // 是否渠道商品
}

// VipGoodsCreateDto 创建VIP商品DTO
type VipGoodsCreateDto struct {
	GoodsId        string           `json:"goods_id" binding:"required"`             // 商品id
	Title          string           `json:"title" binding:"required"`                // 商品名称
	Price          float64          `json:"price" binding:"required,min=0"`          // 商品价格
	VipType        int              `json:"vip_type" binding:"required,oneof=1 2 3"` // vip类型;1:月;2:季;3:年
	Version        string           `json:"version" binding:"required"`              // 创建版本
	Channel        string           `json:"channel" binding:"required"`              // 客户端/渠道
	Sort           int              `json:"sort"`                                    // 排序;升序排列
	MonthlyDiamond int              `json:"monthly_diamond"`                         // 月钻石数量
	GiveCount      int              `json:"give_count"`                              // 赠送次数
	IsDisplay      model.StatusFlag `json:"is_display" binding:"oneof=-1 1"`         // 是否显示，-1：否；1：是
	IsDelete       model.StatusFlag `json:"is_delete" binding:"oneof=-1 1"`          // 是否删除，0：未删除；1：已删除
}

// VipGoodsUpdateDto 更新VIP商品DTO
type VipGoodsUpdateDto struct {
	Id             int              `json:"id" binding:"required,min=1"`             // 主键id
	GoodsId        string           `json:"goods_id" binding:"required"`             // 商品id
	Title          string           `json:"title" binding:"required"`                // 商品名称
	Price          float64          `json:"price" binding:"required,min=0"`          // 商品价格
	VipType        int              `json:"vip_type" binding:"required,oneof=1 2 3"` // vip类型;1:月;2:季;3:年
	Version        string           `json:"version" binding:"required"`              // 创建版本
	Channel        string           `json:"channel" binding:"required"`              // 客户端/渠道
	Sort           int              `json:"sort"`                                    // 排序;升序排列
	MonthlyDiamond int              `json:"monthly_diamond"`                         // 月钻石数量
	GiveCount      int              `json:"give_count"`                              // 赠送次数
	IsDisplay      model.StatusFlag `json:"is_display" binding:"oneof=-1 1"`         // 是否显示，-1：否；1：是
}

// VipGoodsQueryDto 查询VIP商品DTO
type VipGoodsQueryDto struct {
	Page      int    `json:"page" form:"page" binding:"min=1"`                   // 页码
	PageSize  int    `json:"page_size" form:"page_size" binding:"min=1,max=100"` // 每页大小
	GoodsId   string `json:"goods_id" form:"goods_id"`                           // 商品id筛选
	Title     string `json:"title" form:"title"`                                 // 商品名称筛选
	VipType   *int   `json:"vip_type" form:"vip_type"`                           // vip类型筛选
	Version   string `json:"version" form:"version"`                             // 创建版本筛选
	Channel   string `json:"channel" form:"channel"`                             // 渠道筛选
	IsDisplay *int8  `json:"is_display" form:"is_display"`                       // 显示状态筛选
	IsDelete  *int8  `json:"is_delete" form:"is_delete"`                         // 删除状态筛选
}

// GoodsVipChannelDto VIP商品渠道DTO
type GoodsVipChannelDto struct {
	ID            int       `json:"id"`              // 主键id
	GoodsID       int       `json:"goods_id"`        // 商品id
	ChannelID     int       `json:"channel_id"`      // 渠道id
	Version       string    `json:"version"`         // 创建版本
	VersionInt    int       `json:"version_int"`     // 版本int值
	GoodsMiddleId string    `json:"goods_middle_id"` // 商品中间id
	CreateAt      time.Time `json:"create_at"`       // 创建时间
	Sort          int       `json:"sort"`            // 排序;升序排列
	IsDelete      int8      `json:"is_delete"`       // 是否已被删除，0：未删除；1：已删除
	// 关联数据
	VipGoodsInfoDto  VipGoodsInfoDto     `json:"vip_goods_info"`    // 关联的VIP商品信息
	MarketingChannel MarketingChannelDto `json:"marketing_channel"` // 关联的营销渠道信息
}

// GoodsVipChannelReq VIP商品渠道请求DTO
type GoodsVipChannelReq struct {
	ID            int    `json:"id" form:"id"`                           // 主键id
	GoodsID       int64  `json:"goods_id" form:"goods_id"`               // 商品id
	ChannelID     int    `json:"channel_id" form:"channel_id"`           // 渠道id
	Version       string `json:"version" form:"version"`                 // 创建版本
	GoodsMiddleId string `json:"goods_middle_id" form:"goods_middle_id"` // 商品中间id

	// GoodsModel 相关筛选条件
	IsDisplay     model.StatusFlag `json:"is_display" form:"is_display"` // 商品是否显示：-1-否，1-是
	GoodsIsDelete model.StatusFlag `json:"is_delete" form:"is_delete"`   // 商品是否删除：0-未删除，1-已删除
	GoodsChannel  string           `json:"channel" form:"channel"`

	// MarketingChannelModel 相关筛选条件
	ChannelIsDeleted model.StatusFlag `json:"is_deleted" form:"is_deleted"` // 渠道是否删除：-1-未删除，1-已删除

	// 查询参数
	Page    int    `json:"page" form:"page"`         // 页码
	Size    int    `json:"size" form:"size"`         // 每页大小
	OrderBy string `json:"order_by" form:"order_by"` // 排序字段
}

// GoodsVipChannelCreateDto 创建VIP商品渠道DTO
type GoodsVipChannelCreateDto struct {
	GoodsID   int    `json:"goods_id" binding:"required,min=1"`   // 商品id
	ChannelID int    `json:"channel_id" binding:"required,min=1"` // 渠道id
	Version   string `json:"version" binding:"required"`          // 创建版本
}

// GoodsVipChannelBatchCreateDto 批量创建VIP商品渠道DTO
type GoodsVipChannelBatchCreateDto struct {
	GoodsID    int    `json:"goods_id" binding:"required,min=1"`    // 商品id
	ChannelIDs []int  `json:"channel_ids" binding:"required,min=1"` // 渠道id列表
	Version    string `json:"version" binding:"required"`           // 创建版本
}

// GoodsVipChannelUpdateDto 更新VIP商品渠道DTO
type GoodsVipChannelUpdateDto struct {
	ID        int    `json:"id" binding:"required,min=1"`            // 主键id
	GoodsID   int    `json:"goods_id" binding:"required,min=1"`      // 商品id
	ChannelID int    `json:"channel_id" binding:"required,min=1"`    // 渠道id
	Version   string `json:"version" binding:"required"`             // 创建版本
	IsDelete  int    `json:"is_delete" binding:"required,oneof=0 1"` // 是否删除：0-未删除，1-已删除
	Sort      int    `json:"sort"`                                   // 排序;升序排列
}

// GoodsVipChannelQueryDto 查询VIP商品渠道DTO
type GoodsVipChannelQueryDto struct {
	Page          int    `json:"page" form:"page" binding:"min=1"`                   // 页码
	PageSize      int    `json:"page_size" form:"page_size" binding:"min=1,max=100"` // 每页大小
	GoodsID       *int   `json:"goods_id" form:"goods_id"`                           // 商品id筛选
	ChannelID     *int   `json:"channel_id" form:"channel_id"`                       // 渠道id筛选
	Version       string `json:"version" form:"version"`                             // 创建版本筛选
	IsDelete      *int   `json:"is_delete" form:"is_delete"`                         // 删除状态筛选
	GoodsMiddleId string `json:"goods_middle_id"`                                    // 商品中间id
}
