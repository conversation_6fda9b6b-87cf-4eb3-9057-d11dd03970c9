package service

import (
	"chongli/internal/service/dto"
	"chongli/pkg/qiniu"
	"crypto/md5"
	"fmt"
	"path"
	"strconv"
	"strings"
	"time"
)

type UploadService struct {
}

func NewUploadService() *UploadService {
	return &UploadService{}
}

// Upload 上传文件
func (a UploadService) Upload(req *dto.UploadRequestDto) (resp string, err error) {
	baseFilename := path.Base(req.File.Filename)
	ext := path.Ext(baseFilename)

	ext = strings.ToLower(ext)

	filenameWithoutExt := strings.TrimSuffix(baseFilename, ext)
	// 将文件名和时间戳进行MD5加密
	newFilename := fmt.Sprintf("%x", md5.Sum([]byte(filenameWithoutExt+strconv.Itoa(int(time.Now().UnixMilli())))))
	filePath := fmt.Sprintf("%s%s%s", time.Now().Format("2006/01/02/"), newFilename, path.Ext(req.File.Filename))
	url, _err := qiniu.UploadFile(0, req.File, filePath)
	if _err != nil {
		return "", _err
	}
	return url, nil
}
