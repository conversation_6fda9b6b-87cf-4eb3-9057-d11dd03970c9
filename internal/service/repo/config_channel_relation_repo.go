package repo

import "chongli/internal/service/dto"

// ConfigChannelRelationRepo 配置渠道关联数据仓库接口
type ConfigChannelRelationRepo interface {
	// List 获取配置渠道关联列表，支持多种条件筛选
	List(*dto.ConfigChannelRelationDto) ([]*dto.ConfigChannelRelationDto, error)

	// CreateConfigChannelRelation 创建配置渠道关联
	CreateConfigChannelRelation(configID int, channelID int) error

	// DeleteConfigChannelRelation 删除配置渠道关联
	DeleteConfigChannelRelation(id int) error

	// GetChannelsByConfigID 根据配置ID获取关联的渠道列表
	GetChannelsByConfigID(configID int) ([]*dto.ConfigChannelRelationDto, error)
}
