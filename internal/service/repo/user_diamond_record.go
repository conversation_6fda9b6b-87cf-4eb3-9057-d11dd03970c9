package repo

import (
	"chongli/internal/service/dto"
	"gorm.io/gorm"
)

// UserDiamondRecordRepo 用户钻石记录数据仓库接口
type UserDiamondRecordRepo interface {
	// CreateUserDiamondRecord 创建用户钻石记录
	CreateUserDiamondRecord(record *dto.UserDiamondRecordDto, tx ...*gorm.DB) error
	// DeleteUserDiamondRecord 删除用户钻石记录（软删除）
	DeleteUserDiamondRecord(id int64) error
	// Page 分页查询用户钻石记录
	Page(req *dto.UserDiamondRecordPageRequest) ([]*dto.UserDiamondRecordDto, int64, error)
	ListDiamondRecordByUserId(userId int64) ([]*dto.UserDiamondRecordDto, error)
}
