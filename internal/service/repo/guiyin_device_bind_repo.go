package repo

import (
	"chongli/internal/service/dto"
)

// GuiyinDeviceBindRepo 设备归因绑定仓库接口
type GuiyinDeviceBindRepo interface {
	// GetGuiyinDeviceBindByID 根据ID获取设备绑定信息
	GetGuiyinDeviceBindByID(id int) (*dto.GuiyinDeviceBindDto, error)

	// GetGuiyinDeviceBindByDeviceID 根据设备ID获取设备绑定信息
	GetGuiyinDeviceBindByDeviceID(deviceId string) (*dto.GuiyinDeviceBindDto, error)

	// GetGuiyinDeviceBindList 获取设备绑定列表，支持多条件查询和分页
	GetGuiyinDeviceBindList(queryDto *dto.GuiyinDeviceBindDto, page, pageSize int) ([]*dto.GuiyinDeviceBindDto, error)

	// GetGuiyinDeviceBindCount 获取设备绑定数量，支持多条件查询
	GetGuiyinDeviceBindCount(queryDto *dto.GuiyinDeviceBindDto) (int64, error)

	// CreateGuiyinDeviceBind 创建设备绑定
	CreateGuiyinDeviceBind(createDto *dto.GuiyinDeviceBindCreateDto) (*dto.GuiyinDeviceBindDto, error)

	// UpdateGuiyinDeviceBind 更新设备绑定
	UpdateGuiyinDeviceBind(updateDto *dto.GuiyinDeviceBindUpdateDto) (*dto.GuiyinDeviceBindDto, error)

	// DeleteGuiyinDeviceBind 删除设备绑定
	DeleteGuiyinDeviceBind(id int) error
}
