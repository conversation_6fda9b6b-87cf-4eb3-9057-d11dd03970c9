package repo

import (
	"chongli/internal/service/dto"
	"gorm.io/gorm"
)

// TemplateCategoryRepo 模板分类数据仓库接口
type TemplateCategoryRepo interface {
	// CreateTemplateCategory 创建模板分类
	CreateTemplateCategory(req *dto.CreateTemplateCategoryRequest, tx ...*gorm.DB) (*dto.TemplateCategoryDto, error)
	// GetTemplateCategoryById 根据ID查询模板分类
	GetTemplateCategoryById(id int64, tx ...*gorm.DB) (*dto.TemplateCategoryDto, error)
	// UpdateTemplateCategory 更新模板分类
	UpdateTemplateCategory(req *dto.UpdateTemplateCategoryRequest, tx ...*gorm.DB) error
	// DeleteTemplateCategory 删除模板分类（软删除）
	DeleteTemplateCategory(id int64, tx ...*gorm.DB) error
	// GetTemplateCategoryByName 根据名称查询模板分类
	GetTemplateCategoryByName(name string, tx ...*gorm.DB) (*dto.TemplateCategoryDto, error)
	// PageTemplateCategory 分页查询模板分类
	PageTemplateCategory(req *dto.TemplateCategoryPageRequest) (*dto.TemplateCategoryPageResponse, error)
	// UpdateTemplateCategoryStatus 更新模板分类状态
	UpdateTemplateCategoryStatus(id int64, isActive int8, tx ...*gorm.DB) error
	// BatchDeleteTemplateCategory 批量删除模板分类
	BatchDeleteTemplateCategory(ids []int64, tx ...*gorm.DB) error
	// GetAllActiveTemplateCategories 获取所有启用的模板分类
	GetAllActiveTemplateCategories(tx ...*gorm.DB) ([]*dto.TemplateCategoryDto, error)
	// ListTemplateCategory 获取模板分类列表
	ListTemplateCategory(version string, mainClassId int64) ([]*dto.TemplateCategoryDto, error)
	// BatchUpdateTemplateCategory 批量更新模板分类
	BatchUpdateTemplateCategory(req *dto.BatchUpdateTemplateCategoryRequest, tx ...*gorm.DB) error
	// GetAllCategory 获取所有模板分类（不分页）
	GetAllCategory(tx ...*gorm.DB) ([]*dto.TemplateCategoryDto, error)
}
