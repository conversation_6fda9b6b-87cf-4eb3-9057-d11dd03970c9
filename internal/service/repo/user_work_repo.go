package repo

import (
	"chongli/internal/service/dto"
	"context"

	"gorm.io/gorm"
)

type UserWorkRepo interface {
	Create(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) error
	// List 根据条件查询用户作品列表,会preload User，支持分页
	List(ctx context.Context, userWork *dto.UserWorks, withTemplate bool, tx ...*gorm.DB) ([]*dto.UserWorks, error)
	// GetOne 根据条件查询单个用户作品,会preload User
	GetOne(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) (*dto.UserWorks, error)
	Delete(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) error
	Update(ctx context.Context, id uint64, updates map[string]any, tx ...*gorm.DB) error
	Count(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) (int64, error)
}
