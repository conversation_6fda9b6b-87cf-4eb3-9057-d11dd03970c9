package repo

import (
	"chongli/internal/service/dto"
)

// PopupRepo 弹窗数据仓库接口
type PopupRepo interface {
	// CreatePopup 创建弹窗
	CreatePopup(popup *dto.PopupCreateDto) (*dto.PopupDto, error)

	// GetPopupByID 根据ID获取弹窗
	GetPopupByID(id int) (*dto.PopupDto, error)

	// GetPopupList 获取弹窗列表
	GetPopupList(query *dto.PopupQueryDto) ([]*dto.PopupDto, error)

	// GetPopupCount 获取弹窗总数
	GetPopupCount(query *dto.PopupQueryDto) (int64, error)

	// UpdatePopup 更新弹窗
	UpdatePopup(popup *dto.PopupUpdateDto) (*dto.PopupDto, error)

	// DeletePopup 删除弹窗（软删除）
	DeletePopup(id int) error

	// GetPopupByLocation 根据位置获取弹窗
	GetPopupByLocation(location string) ([]*dto.PopupDto, error)
}
