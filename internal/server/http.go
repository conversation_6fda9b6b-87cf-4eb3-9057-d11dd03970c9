package server

import (
	"chongli/component"
	adminRouter "chongli/internal/app/admin/router"
	apiRouter "chongli/internal/app/api/router"
	"chongli/internal/middleware"
	"context"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"github.com/nanmu42/gzip"
)

type Router struct {
	// api
	UserApi     *apiRouter.UserRouter
	GuiyinApi   *apiRouter.CommonRouter
	PayApi      *apiRouter.BusinessRouter
	TemplateApi *apiRouter.TemplateRouter

	// admin
	AdminManage    *adminRouter.AdminRouter
	UserManage     *adminRouter.UserManageRouter
	BusinessManage *adminRouter.BusinessRouter
	TemplateManage *adminRouter.TemplateManageRouter
	UserWorkManage *adminRouter.UserWorkRouter
}

// HttpServer HTTP服务器
type HttpServer struct {
	Srv       *http.Server
	bootstrap *component.BootStrap
}

// NewHttpServer 创建HTTP服务器
func NewHttpServer(bootstrap *component.BootStrap) *HttpServer {
	gin.SetMode(gin.ReleaseMode)
	gin.DefaultWriter = io.Discard
	engine := gin.New()

	//engine.Static("/bgo", "./static/bgo/")
	// 使用中间件
	engine.Use(middleware.LoggerConfig(), gzip.DefaultHandler().Gin,
		gin.Recovery(), middleware.Cors(),
		middleware.XRequest(), middleware.SlsLog())
	engine.NoRoute(func(c *gin.Context) {
		// 实现内部重定向
		c.JSON(http.StatusNotFound, gin.H{
			"title": "404 not found",
		})
	})
	engine.Any("/api/v1/ping", func(context *gin.Context) {
		content, _ := os.ReadFile("./.git_version.txt")
		context.JSON(http.StatusOK, &gin.H{
			"code": 0,
			"msg":  string(content),
		})
	})
	srv := &http.Server{
		Addr:         ":8080",
		Handler:      engine,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
	}

	// 初始化所有路由 - 路由实际上在各自的构造函数中已经注册到engine了
	router := initRouter(engine, bootstrap)
	_ = router // 这里主要是为了触发wire的依赖注入
	return &HttpServer{
		Srv:       srv,
		bootstrap: bootstrap,
	}
}

// Start 启动HTTP服务器
func (s *HttpServer) Start() {
	s.bootstrap.Log.Info("Starting HTTP server on :8080")

	go func() {
		if err := s.Srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatal("ListenAndServe error:", err)
		}
	}()

	s.Shutdown()
}

// Shutdown 优雅关闭
func (s *HttpServer) Shutdown() {
	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)

	select {
	case sig := <-sigs:
		fmt.Printf("捕获信号signal.Notify,sigs:%v \n", sig)
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()

		if err := s.Srv.Shutdown(ctx); err != nil {
			fmt.Printf("捕获信号signal.shutdown,err::%v \n", err)
		}
		log.Println("HTTP server shutdown...")
	}

	time.Sleep(3 * time.Second)
}
