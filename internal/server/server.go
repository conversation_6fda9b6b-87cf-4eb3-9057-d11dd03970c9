package server

import (
	"chongli/component"
)

type AppRouter struct {
	Router
}

func makeRouter(router Router) AppRouter {
	return AppRouter{Router: router}
}

// StartHttpServer 启动HTTP服务器的入口函数
func StartHttpServer(bootstrap *component.BootStrap) {
	server := NewHttpServer(bootstrap)
	server.Start()
}

// StartTaskServer 启动任务服务器的入口函数
func StartTaskServer(bootstrap *component.BootStrap) {
	server := NewTaskServer(bootstrap)
	server.Start()
}
