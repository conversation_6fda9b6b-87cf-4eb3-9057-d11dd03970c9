// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package server

import (
	"chongli/component"
	controller2 "chongli/internal/app/admin/controller"
	router2 "chongli/internal/app/admin/router"
	"chongli/internal/app/api/biz"
	"chongli/internal/app/api/controller"
	"chongli/internal/app/api/router"
	"chongli/internal/app/task/processors"
	"chongli/internal/app/task/registry"
	"chongli/internal/dao"
	"chongli/internal/middleware"
	"chongli/internal/service"
	"github.com/gin-gonic/gin"
	"github.com/robfig/cron/v3"
)

import (
	_ "github.com/gin-contrib/pprof"
)

// Injectors from wire.go:

func initRouter(engine *gin.Engine, bootstrap *component.BootStrap) AppRouter {
	userRepo := dao.NewUserRepo(bootstrap)
	configRepo := dao.NewConfigRepo(bootstrap)
	redisRepo := dao.NewRedisRepo(bootstrap)
	configService := service.NewConfigService(bootstrap, configRepo, redisRepo)
	userVipRepo := dao.NewUserVipRepo(bootstrap)
	userLoginService := biz.NewUserLoginService(bootstrap, userRepo, configService, userVipRepo)
	userDiamondRecordRepo := dao.NewUserDiamondRecordRepo(bootstrap)
	userBindGetuiRepo := dao.NewUserBindGetuiRepo(bootstrap)
	goodsVipRepo := dao.NewGoodsVipDao(bootstrap)
	userService := service.NewUserService(bootstrap, configService, userRepo, userDiamondRecordRepo, userVipRepo, userBindGetuiRepo, goodsVipRepo)
	userLoginController := controller.NewUserLoginController(bootstrap, userLoginService, userService)
	userInfoBiz := biz.NewUserInfoBiz(bootstrap, userService, userRepo, userVipRepo, userDiamondRecordRepo)
	userInfoController := controller.NewUserInfoController(bootstrap, userService, userInfoBiz)
	userGiveBiz := biz.NewUserGiveBiz(bootstrap, redisRepo, userService, configService)
	userGiveController := controller.NewUserGiveController(bootstrap, userGiveBiz)
	jwtAuthMiddleware := middleware.NewJWTAuthMiddleware(bootstrap, userService)
	userRouter := router.NewUserRouter(engine, userLoginController, userInfoController, userGiveController, jwtAuthMiddleware)
	guiyinDeviceBindRepo := dao.NewGuiyinDeviceBindRepo(bootstrap)
	guiyinService := service.NewGuiyinService(bootstrap, guiyinDeviceBindRepo)
	configChannelRelationRepo := dao.NewConfigChannelRelationRepo(bootstrap)
	versionRepo := dao.NewVersionRepo(bootstrap)
	configBiz := biz.NewConfigBiz(bootstrap, configRepo, guiyinService, configChannelRelationRepo, versionRepo)
	marketingChannelRepo := dao.NewMarketingChannelRepo(bootstrap)
	guiyinApiService := biz.NewGuiyinService(guiyinDeviceBindRepo, configService, marketingChannelRepo)
	channelPopupRepo := dao.NewChannelPopupRepo(bootstrap)
	popupBiz := biz.NewPopupBiz(bootstrap, guiyinService, channelPopupRepo)
	geTuiBiz := biz.NewGeTuiBiz(userBindGetuiRepo)
	bannerRepo := dao.NewBannerRepo(bootstrap)
	bannerBiz := biz.NewBannerBiz(bootstrap, bannerRepo)
	commonService := service.NewCommonService()
	commonController := controller.NewCommonController(bootstrap, configBiz, guiyinApiService, popupBiz, geTuiBiz, bannerBiz, commonService)
	commonRouter := router.NewCommonRouter(engine, commonController, jwtAuthMiddleware)
	goodsBiz := biz.NewGoodsBiz(guiyinService, goodsVipRepo)
	goodsController := controller.NewGoodsController(bootstrap, goodsBiz, configService)
	payOrderRepo := dao.NewPayOrderRepo(bootstrap)
	payOrderService := biz.NewPayOrderService(bootstrap, guiyinService, payOrderRepo, goodsVipRepo, userRepo, guiyinDeviceBindRepo, redisRepo, marketingChannelRepo)
	payOrderController := controller.NewPayOrderController(bootstrap, payOrderService)
	payNotifyService := service.NewPayNotifyService(bootstrap, payOrderRepo, goodsVipRepo, userRepo, userVipRepo, userService, guiyinService, configService)
	payNotifyController := controller.NewPayNotifyController(bootstrap, payNotifyService)
	businessRouter := router.NewBusinessRouter(engine, goodsController, payOrderController, payNotifyController, jwtAuthMiddleware)
	templateCategoryRepo := dao.NewTemplateCategoryRepo(bootstrap)
	templateCategoryBiz := biz.NewTemplateCategoryBiz(bootstrap, templateCategoryRepo)
	templateCategoryController := controller.NewTemplateCategoryController(bootstrap, templateCategoryBiz)
	templateAIRepo := dao.NewTemplateAIRepo(bootstrap)
	templateBiz := biz.NewTemplateBiz(bootstrap, templateAIRepo)
	templateController := controller.NewTemplateController(bootstrap, templateBiz)
	userWorkRepo := dao.NewUserWorkRepo(bootstrap)
	taskStepRepo := dao.NewTaskStepRepo(bootstrap)
	userWorkService := service.NewUserWorkService(bootstrap, userWorkRepo, userService)
	taskService := service.NewTaskService(bootstrap, taskStepRepo, userWorkRepo, userService, userWorkService)
	templateService := service.NewTemplateService(templateAIRepo, templateCategoryRepo, bootstrap)
	userWorkBiz := biz.NewUserWorkBiz(userWorkRepo, taskService, taskStepRepo, bootstrap, userService, templateService)
	userWorksController := controller.NewUserWorksController(bootstrap, userWorkBiz)
	templateRouter := router.NewTemplateRouter(engine, templateCategoryController, templateController, userWorksController, jwtAuthMiddleware)
	authController := controller2.NewAuthController(bootstrap)
	accountController := controller2.NewAccountController(bootstrap)
	channelController := controller2.NewChannelController(bootstrap)
	configController := controller2.NewConfigController(bootstrap)
	popupController := controller2.NewPopupController(bootstrap)
	controllerCommonController := controller2.NewCommonController(bootstrap)
	channelPopupController := controller2.NewChannelPopupController(bootstrap, channelPopupRepo)
	configChannelRelationController := controller2.NewConfigChannelRelationController(bootstrap)
	versionController := controller2.NewVersionController(versionRepo)
	bannerController := controller2.NewBannerController(bootstrap, bannerRepo)
	adminRouter := router2.NewAdminRouter(engine, authController, accountController, channelController, configController, popupController, controllerCommonController, channelPopupController, configChannelRelationController, versionController, bannerController)
	userManageController := controller2.NewUserManageController(bootstrap, userService, userRepo, userVipRepo, userDiamondRecordRepo)
	guiYinController := controller2.NewGuiYinController(guiyinDeviceBindRepo, guiyinService)
	userManageRouter := router2.NewUserManageRouter(engine, userManageController, guiYinController)
	controllerGoodsController := controller2.NewGoodsController(goodsVipRepo)
	goodsBaseController := controller2.NewGoodsBaseController(goodsVipRepo)
	payOrderManageController := controller2.NewPayOrderManageController(bootstrap, payOrderRepo, payNotifyService)
	routerBusinessRouter := router2.NewBusinessRouter(engine, controllerGoodsController, goodsBaseController, payOrderManageController)
	templateManageController := controller2.NewTemplateManageController(bootstrap, templateCategoryRepo, configRepo)
	templateAIController := controller2.NewTemplateAIController(templateAIRepo)
	templateManageRouter := router2.NewTemplateManageRouter(engine, templateManageController, templateAIController)
	userWorkController := controller2.NewUserWorkController(bootstrap, userWorkRepo, taskStepRepo)
	userWorkRouter := router2.NewUserWorkRouter(engine, userWorkController)
	serverRouter := Router{
		UserApi:        userRouter,
		GuiyinApi:      commonRouter,
		PayApi:         businessRouter,
		TemplateApi:    templateRouter,
		AdminManage:    adminRouter,
		UserManage:     userManageRouter,
		BusinessManage: routerBusinessRouter,
		TemplateManage: templateManageRouter,
		UserWorkManage: userWorkRouter,
	}
	appRouter := makeRouter(serverRouter)
	return appRouter
}

func initTask(c *cron.Cron, bootstrap *component.BootStrap) *TimerTask {
	userRepo := dao.NewUserRepo(bootstrap)
	userVipRepo := dao.NewUserVipRepo(bootstrap)
	configRepo := dao.NewConfigRepo(bootstrap)
	redisRepo := dao.NewRedisRepo(bootstrap)
	configService := service.NewConfigService(bootstrap, configRepo, redisRepo)
	userDiamondRecordRepo := dao.NewUserDiamondRecordRepo(bootstrap)
	userBindGetuiRepo := dao.NewUserBindGetuiRepo(bootstrap)
	goodsVipRepo := dao.NewGoodsVipDao(bootstrap)
	userService := service.NewUserService(bootstrap, configService, userRepo, userDiamondRecordRepo, userVipRepo, userBindGetuiRepo, goodsVipRepo)
	userTaskProcessors := processors.NewUserTaskProcessors(bootstrap, userRepo, userVipRepo, configRepo, userService)
	userTask := registry.NewUserTask(c, bootstrap, userTaskProcessors)
	payOrderRepo := dao.NewPayOrderRepo(bootstrap)
	payOrderTaskProcessors := processors.NewPayOrderTaskProcessors(bootstrap, payOrderRepo)
	payOrderTask := registry.NewPayOrderTask(c, bootstrap, payOrderTaskProcessors)
	taskStepRepo := dao.NewTaskStepRepo(bootstrap)
	userWorkRepo := dao.NewUserWorkRepo(bootstrap)
	userWorkService := service.NewUserWorkService(bootstrap, userWorkRepo, userService)
	taskService := service.NewTaskService(bootstrap, taskStepRepo, userWorkRepo, userService, userWorkService)
	picTaskProcessors := processors.NewPicTaskProcessors(bootstrap, taskService, userWorkService, userService, configService)
	danceTaskProcessors := processors.NewDanceTaskProcessors(bootstrap, taskService, userWorkRepo, userWorkService, userService, configService)
	avatarPictureProcessors := processors.NewAvatarPictureProcessors(bootstrap, taskService, userWorkService, userService, configService)
	picTask := registry.NewPicTask(c, bootstrap, picTaskProcessors, danceTaskProcessors, avatarPictureProcessors)
	task := &Task{
		UserTask:     userTask,
		PayOrderTask: payOrderTask,
		PicTask:      picTask,
	}
	timerTask := &TimerTask{
		C:    c,
		Task: task,
	}
	return timerTask
}
