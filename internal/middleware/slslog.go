package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"net/url"
	"strings"
	"time"

	"chongli/pkg/aliyun"

	"github.com/ahmetb/go-linq/v3"
	"github.com/gin-gonic/gin"
)

// pathsWithNoSLSLog 不需要打接口请求和响应日志的接口路径.
var pathsWithNoSLSLog = []string{
	"/api/admin/sls/log/query",
	"/api/admin/page/log",
}

// SlsLog sls log.
func SlsLog() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		betTime := time.Now().UnixMilli()
		xRequestID := ctx.GetString("X-Request-Id")

		URL, _ := url.Parse(ctx.Request.RequestURI)

		if linq.From(pathsWithNoSLSLog).Contains(URL.Path) {
			ctx.Next()
			return
		}

		body, _ := ctx.GetRawData()
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))

		// write back.
		blw := &CustomResponseWriter{body: bytes.NewBufferString(""), ResponseWriter: ctx.Writer}
		ctx.Writer = blw

		ctx.Next()

		// 在请求完成后获取所有信息并打印日志
		endTime := time.Now().UnixMilli()

		// 获取请求体信息
		bodyStr := ""
		//判断请求是文件
		if !strings.HasPrefix(ctx.Request.Header.Get("Content-Type"), "multipart/form-data") {
			bodyStr = string(body)
		} else {
			bodyStr = ""
		}
		bodyStr = strings.ReplaceAll(strings.ReplaceAll(bodyStr, "\r", ""), "\n", "")

		// 获取响应信息
		reason := ctx.GetString(`reason`)
		responseStr := strings.ReplaceAll(strings.ReplaceAll(blw.body.String(), "\r", ""), "\n", "")

		// 合并请求和响应日志
		combinedLog := CombinedLog{
			RequestURI:    ctx.Request.RequestURI,
			RequestBody:   bodyStr,
			TimeConsuming: endTime - betTime,
			ResponseBody:  responseStr,
			Reason:        reason,
		}
		combinedBytes, _ := json.Marshal(combinedLog)
		aliyun.SLSWithRequestID(xRequestID, URL.Path, string(combinedBytes))
	}
}

// CombinedLog 合并的请求响应日志
type CombinedLog struct {
	RequestURI    string `json:"request_uri"`
	RequestBody   string `json:"request_body"`
	TimeConsuming int64  `json:"time_consuming"`
	ResponseBody  string `json:"response_body"`
	Reason        string `json:"reason"`
}

// CustomResponseWriter custom response writer.
type CustomResponseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w CustomResponseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// WriteString write string.
func (w CustomResponseWriter) WriteString(s string) (int, error) {
	w.body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}
