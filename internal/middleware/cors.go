package middleware

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

// Cors 跨域配置
func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		c.Header("Access-Control-Allow-Origin", "*")
		//c.<PERSON><PERSON>("Access-Control-Allow-Headers", "*")
		c.<PERSON>("Access-Control-Allow-Headers", "*,content-type,x-token")
		c.<PERSON><PERSON>("Access-Control-Expose-Headers", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		c<PERSON><PERSON>("Access-Control-Max-Age", "86400")
		//放行索引options
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}
		//处理请求
		c.Next()
	}
}
