package middleware

import (
	errpkg "chongli/pkg/error"
	response2 "chongli/pkg/response"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/gookit/goutil/goinfo"
	"strings"
)

// Recovery recover
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, err any) {
		var rErr error
		var _err errpkg.IError
		if e, ok := err.(error); ok {
			rErr = errors.New(e.Error() + "\r\n" + strings.Join(goinfo.GetCallersInfo(3, 13), "\r\n"))
		} else {
			rErr = errors.New(err.(string) + "\r\n" + strings.Join(goinfo.GetCallersInfo(3, 13), "\r\n"))
		}

		uri := c.Request.URL.Path
		method := c.Request.Method

		paramMap := make(map[string]string)

		queryParams := c.Request.URL.Query().Encode()

		paramMap["param"] = queryParams

		data, _ := c.GetRawData()

		paramMap["body"] = string(data)

		_err = errpkg.NewHighErrorWithCause(rErr, response2.SystemBusiness)

		exe := fmt.Sprintf("panic uri: %s,pannic method: %s,param: %v, err info: %v", uri, method, paramMap, rErr.Error())

		reqMap := make(map[string]interface{})

		reqMap["panicContext"] = exe

		response2.Response(c, reqMap, response2.SystemBusiness, _err, response2.WithSLSLog)

		c.Abort()
	})
}
