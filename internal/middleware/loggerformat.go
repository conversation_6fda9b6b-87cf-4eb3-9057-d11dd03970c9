package middleware

import (
	"chongli/pkg/logger"
	"fmt"
	"os"
	"time"

	"github.com/gin-gonic/gin"
)

// LoggerConfig logger config.
func LoggerConfig() gin.HandlerFunc {
	return gin.LoggerWithConfig(gin.LoggerConfig{
		Formatter: func(param gin.LogFormatterParams) string {
			var statusColor, methodColor, resetColor string
			if param.IsOutputColor() {
				statusColor = param.StatusCodeColor()
				methodColor = param.MethodColor()
				resetColor = param.ResetColor()
			}

			if param.Latency > time.Minute {
				// Truncate in a golang < 1.8 safe way
				param.Latency = param.Latency - param.Latency%time.Second
			}
			lName, _ := os.Hostname()
			return fmt.Sprintf("%s||| %v |||%v | %v |%s %3d %s| %13v | %15s |%s %-7s %s %#v\n%s",
				logger.HTTPPort,
				lName,
				param.TimeStamp.Format("2006-01-02"),
				param.TimeStamp.Format("15:04:05"),
				statusColor,
				param.StatusCode,
				resetColor,
				param.Latency,
				param.ClientIP,
				methodColor, param.Method, resetColor,
				param.Path,
				param.ErrorMessage,
			)
		},
	})
}
