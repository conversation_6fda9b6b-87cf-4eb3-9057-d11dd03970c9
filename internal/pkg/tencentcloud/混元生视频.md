# 提交图片唱演任务

## 1. 接口描述

接口请求域名： vclm.tencentcloudapi.com 。

用于提交图片唱演任务。
支持提交音频和图片生成唱演视频，满足社交娱乐、互动营销等场景的需求。

## 2. 输入参数

| 参数名称    | 必选 | 类型                                                         | 描述                                                         |
| :---------- | :--- | :----------------------------------------------------------- | :----------------------------------------------------------- |
| Action      | 是   | String                                                       | [公共参数](https://cloud.tencent.com/document/api/1616/107789)，本接口取值：SubmitPortraitSingJob。 |
| Version     | 是   | String                                                       | [公共参数](https://cloud.tencent.com/document/api/1616/107789)，本接口取值：2024-05-23。 |
| Region      | 是   | String                                                       | [公共参数](https://cloud.tencent.com/document/api/1616/107789)，详见产品支持的 [地域列表](https://cloud.tencent.com/document/api/1616/107789#.E5.9C.B0.E5.9F.9F.E5.88.97.E8.A1.A8)。 |
| AudioUrl    | 是   | String                                                       | 传入音频URL地址，音频要求： - 音频时长：2秒 - 60秒 - 音频格式：mp3、wav、m4a 示例值：https://cos.ap-guangzhou.myqcloud.com/audio.mp3 |
| ImageUrl    | 否   | String                                                       | 传入图片URL地址，图片要求： - 图片格式：jpg、jpeg、png、bmp、webp - 图片分辨率：192～4096 - 图片大小：不超过10M - 图片宽高比：图片【宽：高】在1:2到2:1范围内 - 图片内容：避免上传无人脸、无宠物脸或脸部过小、不完整、不清晰、偏转角度过大、嘴部被遮挡的图片。 示例值：https://cos.ap-guangzhou.myqcloud.com/image.jpg |
| ImageBase64 | 否   | String                                                       | 传入图片Base64编码，编码后请求体大小不超过10M。 图片Base64编码与URL地址必传其一，如果都传以ImageBase64为准。 示例值：/9j/4QlQaHR0c...N6a2M5ZCI |
| Mode        | 否   | String                                                       | 唱演模式，默认使用人像模式。 Person：人像模式，仅支持上传人像图片，人像生成效果更好，如果图中未检测到有效人脸将被拦截，生成时会将视频短边分辨率放缩至512。 Pet：宠物模式，支持宠物等非人像图片，固定生成512:512分辨率视频。 示例值：Person |
| Resolution  | 否   | String                                                       | 生成视频尺寸。可选取值："512:512"。  人像模式下，如果不传该参数，默认生成视频的短边分辨率为512，长边分辨率不固定、由模型根据生成效果自动适配得到。如需固定生成分辨率可传入512:512。  宠物模式下，如果不传该参数，默认将脸部唱演视频回贴原图，生成视频分辨率与原图一致。如不需要脸部回贴，仅保留脸部唱演视频，可传入512:512。 示例值：512:512 |
| LogoAdd     | 否   | Integer                                                      | 为生成视频添加标识的开关，默认为1。 1：添加标识； 0：不添加标识； 其他数值：默认按1处理。 建议您使用显著标识来提示，该视频是 AI 生成的视频。 示例值：1 |
| LogoParam   | 否   | [LogoParam](https://cloud.tencent.com/document/api/1616/107808#LogoParam) | 标识内容设置。 默认在生成视频的右下角添加“视频由 AI 生成”字样，您可根据自身需要替换为其他的标识图片。 示例值：{"LogoUrl": "https://cos.ap-guangzhou.myqcloud.com/logo.jpg", "LogoRect": {"X": 10, "Y": 10, "Width": 20, "Height": 20}} |

## 3. 输出参数

| 参数名称  | 类型   | 描述                                                         |
| :-------- | :----- | :----------------------------------------------------------- |
| JobId     | String | 任务ID。任务有效期为48小时。 示例值：1199999431088685056     |
| RequestId | String | 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。 |

## 4. 示例

### 示例1 调用成功-图片url地址

调用成功

#### 输入示例



```
POST / HTTP/1.1
Host: vclm.tencentcloudapi.com
Content-Type: application/json
X-TC-Action: SubmitPortraitSingJob
<公共请求参数>

{
    "AudioUrl": "https://***/test.mp3",
    "ImageUrl": "https://***/test.png"
}
```



#### 输出示例



```json
{
    "Response": {
        "JobId": "1199964964965990400",
        "RequestId": "79655032-c347-4f05-af23-ae80f7ff47eb"
    }
}
```



### 示例2 调用失败-音频时长超限

音频时长超限

#### 输入示例



```
POST / HTTP/1.1
Host: vclm.tencentcloudapi.com
Content-Type: application/json
X-TC-Action: SubmitPortraitSingJob
<公共请求参数>

{
    "AudioUrl": "https://***/durationInvalid.m4a",
    "ImageUrl": "https://***/aj.jpg"
}
```



#### 输出示例



```json
{
    "Response": {
        "Error": {
            "Code": "InvalidParameterValue.InvalidAudioDuration",
            "Message": "音频时长超出限定范围。上传音频的时长要求：在1秒到60秒范围内"
        },
        "RequestId": "9d314f18-3670-4793-bf4b-619b43494ac7"
    }
}
```



### 示例3 调用失败-图片分辨率超限

图片分辨率超限

#### 输入示例



```
POST / HTTP/1.1
Host: vclm.tencentcloudapi.com
Content-Type: application/json
X-TC-Action: SubmitPortraitSingJob
<公共请求参数>

{
    "AudioUrl": "https://***/test.mp3",
    "ImageUrl": "https://***/resolutionInvalid.jpg"
}
```



#### 输出示例



```json
{
    "Response": {
        "Error": {
            "Code": "InvalidParameterValue.InvalidImageResolution",
            "Message": "图片分辨率超出限定范围。上传图片的长边分辨率要求：在0到2560范围内"
        },
        "RequestId": "29498231-20eb-43d1-bb3b-cc9e07978699"
    }
}
```



### 示例4 调用失败-图片宽高比超限

图片宽高比超限

#### 输入示例



```
POST / HTTP/1.1
Host: vclm.tencentcloudapi.com
Content-Type: application/json
X-TC-Action: SubmitPortraitSingJob
<公共请求参数>

{
    "AudioUrl": "https://***/test.mp3",
    "ImageUrl": "https://***/ratioInvalid.jpg"
}
```



#### 输出示例



```json
{
    "Response": {
        "Error": {
            "Code": "InvalidParameterValue.InvalidImageAspectRatio",
            "Message": "图片宽高比超出限定范围。上传图片的宽高比要求：在0.5到2.0范围内"
        },
        "RequestId": "3c9cb9bd-5b2a-4b18-a890-73f7712ca2ce"
    }
}
```



### 示例5 调用失败-图片大小超限

图片大小超限

#### 输入示例



```
POST / HTTP/1.1
Host: vclm.tencentcloudapi.com
Content-Type: application/json
X-TC-Action: SubmitPortraitSingJob
<公共请求参数>

{
    "AudioUrl": "https://***/test.mp3",
    "ImageUrl": "https://***/storageInvalid.jpg"
}
```



#### 输出示例



```json
{
    "Response": {
        "Error": {
            "Code": "InvalidParameterValue.InvalidImageSize",
            "Message": "图片大小超出限定范围。上传图片的大小要求：在0MB到10MB范围内"
        },
        "RequestId": "cd17e6bb-afdb-4bed-8a15-55c461333104"
    }
}
```



# 查询图片唱演任务

## 1. 接口描述

接口请求域名： vclm.tencentcloudapi.com 。

用于查询图片唱演任务。
支持提交音频和图片生成唱演视频，满足社交娱乐、互动营销等场景的需求。

## 2. 输入参数

| 参数名称 | 必选 | 类型   | 描述                                                         |
| :------- | :--- | :----- | :----------------------------------------------------------- |
| Action   | 是   | String | [公共参数](https://cloud.tencent.com/document/api/1616/107789)，本接口取值：DescribePortraitSingJob。 |
| Version  | 是   | String | [公共参数](https://cloud.tencent.com/document/api/1616/107789)，本接口取值：2024-05-23。 |
| Region   | 是   | String | [公共参数](https://cloud.tencent.com/document/api/1616/107789)，详见产品支持的 [地域列表](https://cloud.tencent.com/document/api/1616/107789#.E5.9C.B0.E5.9F.9F.E5.88.97.E8.A1.A8)。 |
| JobId    | 是   | String | 任务ID 示例值：1199999431088685056                           |

## 3. 输出参数

| 参数名称       | 类型   | 描述                                                         |
| :------------- | :----- | :----------------------------------------------------------- |
| JobId          | String | 任务ID 示例值：1199999431088685056                           |
| StatusCode     | String | 任务状态码 —RUN：处理中 —FAIL：处理失败 —STOP：处理终止 —DONE：处理完成 示例值：["RUN","FAIL","STOP","DONE"] |
| StatusMsg      | String | 任务状态信息 示例值：["处理中","处理失败","处理中止","处理完成"] |
| ErrorCode      | String | 任务执行错误码。当任务状态不为FAIL时，该值为""。 示例值：FailedOperation.ModerationFailed |
| ErrorMessage   | String | 任务执行错误信息。当任务状态不为FAIL时，该值为""。 示例值：审核未通过 |
| ResultVideoUrl | String | 生成视频的URL地址。有效期24小时。 示例值：https://vcg-prod-xxx.cos.ap-guangzhou.tencentcos.cn/xxx.mp4 |
| RequestId      | String | 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。 |

## 4. 示例

### 示例1 调用成功-任务完成

任务完成

#### 输入示例



```
POST / HTTP/1.1
Host: vclm.tencentcloudapi.com
Content-Type: application/json
X-TC-Action: DescribePortraitSingJob
<公共请求参数>

{
    "JobId": "1199964964965990400"
}
```



#### 输出示例



```json
{
    "Response": {
        "JobId": "1199964964965990400",
        "RequestId": "2564192f-1af0-4d1d-ac92-fac0f5844f73",
        "ResultVideoUrl": "https://***.mp4",
        "StatusCode": "DONE",
        "StatusMsg": "处理完成"
    }
}
```