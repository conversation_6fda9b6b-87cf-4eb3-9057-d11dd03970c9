package tencentcloud

import (
	"context"
	"fmt"
	"testing"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	facefusion "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/facefusion/v20220927"
)

func TestFaceFusionClient_Integration(t *testing.T) {
	client, err := NewFaceFusionClient("AKIDLYYKvJSWxCF6kDVZUM0SvCJEyZ1HKBsT", "fBbAc1OYVGpBJDvRjnfKl16zhCWeaJp7", "ap-chengdu")
	if err != nil {
		t.Fatal(err)
	}

	request := &FuseFaceProRequest{
		RspImgType: common.StringPtr("url"),
		ModelUrl:   common.StringPtr("https://musicbox-cdn.51wnl-cq.com/test/2.png?11111"),
		MergeInfos: []*facefusion.MergeInfo{
			{
				Url: common.StringPtr("https://musicbox-cdn.51wnl-cq.com/test/face3.jpg?11111"),
			},
		},
		LogoAdd: common.Int64Ptr(1),
		FusionUltraParam: &facefusion.FusionUltraParam{
			WarpRadio: common.Float64Ptr(0.8),
		},
	}

	ctx := context.Background()
	resp, err := client.FuseFacePro(ctx, request)
	if err != nil {
		fmt.Println(err)
		t.Fatal(err)
	}
	fmt.Println(resp)
}
