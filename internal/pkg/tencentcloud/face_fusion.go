package tencentcloud

import (
	"context"
	"errors"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tcerrors "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	tchttp "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/http"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/json"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	facefusion "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/facefusion/v20220927"
)

const APIVersion = "2022-09-27"

type FaceFusionClient struct {
	client *facefusion.Client
}

type FuseFaceProRequest struct {
	*tchttp.BaseRequest
	RspImgType       *string                      `json:"RspImgType,omitnil,omitempty" name:"RspImgType"`
	MergeInfos       []*facefusion.MergeInfo      `json:"MergeInfos,omitnil,omitempty" name:"MergeInfos"`
	ModelUrl         *string                      `json:"ModelUrl,omitnil,omitempty" name:"ModelUrl"`
	ModelImage       *string                      `json:"ModelImage,omitnil,omitempty" name:"ModelImage"`
	LogoAdd          *int64                       `json:"LogoAdd,omitnil,omitempty" name:"LogoAdd"`
	LogoParam        *facefusion.LogoParam        `json:"LogoParam,omitnil,omitempty" name:"LogoParam"`
	FuseParam        *facefusion.FuseParam        `json:"FuseParam,omitnil,omitempty" name:"FuseParam"`
	FusionUltraParam *facefusion.FusionUltraParam `json:"FusionUltraParam,omitnil,omitempty" name:"FusionUltraParam"`
}

func (r *FuseFaceProRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *FuseFaceProRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "RspImgType")
	delete(f, "MergeInfos")
	delete(f, "ModelUrl")
	delete(f, "ModelImage")
	delete(f, "LogoAdd")
	delete(f, "LogoParam")
	delete(f, "FuseParam")
	if len(f) > 0 {
		return errors.New("FuseFaceProRequest has unknown keys")
	}
	return json.Unmarshal([]byte(s), &r)
}

func NewFuseFaceProRequest() *FuseFaceProRequest {
	request := &FuseFaceProRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("facefusion", APIVersion, "FuseFacePro")
	return request
}

type FuseFaceProResponseParams struct {
	FusedImage *string `json:"FusedImage,omitnil,omitempty" name:"FusedImage"`
	RequestId  *string `json:"RequestId,omitnil,omitempty" name:"RequestId"`
}

type FuseFaceProResponse struct {
	*tchttp.BaseResponse
	Response *FuseFaceProResponseParams `json:"Response"`
}

func (r *FuseFaceProResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *FuseFaceProResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

func NewFuseFaceProResponse() *FuseFaceProResponse {
	return &FuseFaceProResponse{
		BaseResponse: &tchttp.BaseResponse{},
		Response:     &FuseFaceProResponseParams{},
	}
}

func NewFaceFusionClient(secretId, secretKey, region string) (*FaceFusionClient, error) {
	credential := common.NewCredential(secretId, secretKey)
	cpf := profile.NewClientProfile()
	if len(region) == 0 {
		region = "ap-chengdu"
	}
	cpf.HttpProfile.Endpoint = "facefusion.tencentcloudapi.com"
	client, err := facefusion.NewClient(credential, region, cpf)
	if err != nil {
		return nil, err
	}
	return &FaceFusionClient{client: client}, nil
}

func (c *FaceFusionClient) FuseFacePro(ctx context.Context, request *FuseFaceProRequest) (*FuseFaceProResponse, *tcerrors.TencentCloudSDKError) {
	if request == nil {
		request = NewFuseFaceProRequest()
	}

	c.client.InitBaseRequest(&request.BaseRequest, "facefusion", APIVersion, "FuseFacePro")

	if c.client.GetCredential() == nil {
		return nil, &tcerrors.TencentCloudSDKError{
			Code:    "",
			Message: "FuseFacePro require credential",
		}
	}

	request.SetContext(ctx)

	response := NewFuseFaceProResponse()
	err := c.client.Send(request, response)
	if err != nil {
		return nil, err.(*tcerrors.TencentCloudSDKError)
	}
	return response, nil
}
