package tencentcloud

import (
	"fmt"
	"testing"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	facefusion "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/facefusion/v20220927"
)

func TestFaceFusionClient_Integration2(t *testing.T) {
	// 读取本地image.png文件并转换为base64
	// imageData, err := os.ReadFile("image.png")
	// if err != nil {
	// 	t.Fatalf("读取图片文件失败: %v", err)
	// }
	// imageBase64 := base64.StdEncoding.EncodeToString(imageData)
	credential := common.NewCredential("AKIDLYYKvJSWxCF6kDVZUM0SvCJEyZ1HKBsT", "fBbAc1OYVGpBJDvRjnfKl16zhCWeaJp7")
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "facefusion.tencentcloudapi.com"
	client, err := facefusion.NewClient(credential, "ap-chengdu", cpf)
	if err != nil {
		t.Fatal(err)
	}
	req := facefusion.NewFuseFaceUltraRequest()

	//响应方式url
	req.RspImgType = common.StringPtr("url")

	//人脸的图啊 - 使用读取的base64图片数据
	MergeInfos := []*facefusion.MergeInfo{
		{
			// Image: common.StringPtr(imageBase64),
			Url: common.StringPtr("https://musicbox-cdn.51wnl-cq.com/test/face1.png?11111"),
		},
	}
	// 想要替换的图
	req.ModelUrl = common.StringPtr("https://musicbox-cdn.51wnl-cq.com/test/2.png?11111")

	req.MergeInfos = MergeInfos
	req.SwapModelType = common.Int64Ptr(4)
	req.FusionUltraParam = &facefusion.FusionUltraParam{
		WarpRadio: common.Float64Ptr(1),
	}

	resp, err := client.FuseFaceUltra(req)
	if err != nil {
		fmt.Println(err)
		t.Fatal(err)
	}
	fmt.Println(*resp.Response.FusedImage)
}
