package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"errors"
	"fmt"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type popupRepo struct {
	log *logger.Logger
	db  *gorm.DB
	rds *redis.Client
}

func NewPopupRepo(bootStrap *component.BootStrap) repo.PopupRepo {
	return &popupRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
		rds: bootStrap.Driver.GetRdsClient(),
	}
}

func (d *popupRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 {
		return tx[0]
	}
	return d.db
}

// CreatePopup 创建弹窗
func (d *popupRepo) CreatePopup(popup *dto.PopupCreateDto) (*dto.PopupDto, error) {
	modelPopup := &model.PopupModel{
		Location:   popup.Location,
		Jump:       popup.Jump,
		JumpParam:  popup.JumpParam,
		PopupImg:   popup.PopupImg,
		PopupVideo: popup.PopupVideo,
		Title:      popup.Title,
		Content:    popup.Content,
		IsDelete:   popup.IsDelete,
	}

	err := d.db.Create(modelPopup).Error
	if err != nil {
		d.log.Error("创建弹窗失败: %v", err)
		return nil, err
	}

	return d.convertToDto(modelPopup), nil
}

// GetPopupByID 根据ID获取弹窗
func (d *popupRepo) GetPopupByID(id int) (*dto.PopupDto, error) {
	var popup model.PopupModel
	err := d.db.Where("id = ?", id).First(&popup).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据ID查询弹窗失败: %v", err)
		return nil, err
	}

	return d.convertToDto(&popup), nil
}

// GetPopupList 获取弹窗列表
func (d *popupRepo) GetPopupList(query *dto.PopupQueryDto) ([]*dto.PopupDto, error) {
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 {
		query.PageSize = 10
	}

	queryBuilder := d.db.Model(&model.PopupModel{})
	queryBuilder = d.buildQueryCondition(queryBuilder, query)

	var popups []*model.PopupModel
	offset := (query.Page - 1) * query.PageSize
	err := queryBuilder.Offset(offset).Limit(query.PageSize).Order("id DESC").Find(&popups).Error
	if err != nil {
		d.log.Error("查询弹窗列表失败: %v", err)
		return nil, err
	}

	var popupDtos []*dto.PopupDto
	for _, popup := range popups {
		popupDtos = append(popupDtos, d.convertToDto(popup))
	}

	return popupDtos, nil
}

// GetPopupCount 获取弹窗总数
func (d *popupRepo) GetPopupCount(query *dto.PopupQueryDto) (int64, error) {
	queryBuilder := d.db.Model(&model.PopupModel{})
	queryBuilder = d.buildQueryCondition(queryBuilder, query)

	var total int64
	err := queryBuilder.Count(&total).Error
	if err != nil {
		d.log.Error("查询弹窗总数失败: %v", err)
		return 0, err
	}

	return total, nil
}

// UpdatePopup 更新弹窗
func (d *popupRepo) UpdatePopup(popup *dto.PopupUpdateDto) (*dto.PopupDto, error) {
	existingPopup, err := d.GetPopupByID(popup.ID)
	if err != nil {
		return nil, err
	}
	if existingPopup == nil {
		return nil, fmt.Errorf("弹窗不存在，ID: %d", popup.ID)
	}

	updates := map[string]any{
		"location":    popup.Location,
		"jump":        popup.Jump,
		"jump_param":  popup.JumpParam,
		"popup_img":   popup.PopupImg,
		"popup_video": popup.PopupVideo,
		"title":       popup.Title,
		"content":     popup.Content,
		"is_delete":   popup.IsDelete,
	}

	err = d.db.Model(&model.PopupModel{}).Where("id = ?", popup.ID).Updates(updates).Error
	if err != nil {
		d.log.Error("更新弹窗失败: %v", err)
		return nil, err
	}

	return d.GetPopupByID(popup.ID)
}

// DeletePopup 删除弹窗（软删除）
func (d *popupRepo) DeletePopup(id int) error {
	err := d.db.Model(&model.PopupModel{}).Where("id = ?", id).Update("is_delete", 1).Error
	if err != nil {
		d.log.Error("删除弹窗失败: %v", err)
		return err
	}
	return nil
}

// GetPopupByLocation 根据位置获取弹窗
func (d *popupRepo) GetPopupByLocation(location string) ([]*dto.PopupDto, error) {
	var popups []*model.PopupModel
	err := d.db.Where("location = ? AND is_delete = ?", location, model.StatusEnabled).Find(&popups).Error
	if err != nil {
		d.log.Error("根据位置查询弹窗失败: %v", err)
		return nil, err
	}

	var popupDtos []*dto.PopupDto
	for _, popup := range popups {
		popupDtos = append(popupDtos, d.convertToDto(popup))
	}

	return popupDtos, nil
}

// buildQueryCondition 构建查询条件
func (d *popupRepo) buildQueryCondition(query *gorm.DB, queryDto *dto.PopupQueryDto) *gorm.DB {
	if queryDto.ID > 0 {
		query = query.Where("id = ?", queryDto.ID)
	}

	if queryDto.Location != "" {
		query = query.Where("location LIKE ?", "%"+queryDto.Location+"%")
	}

	if queryDto.Title != "" {
		query = query.Where("title LIKE ?", "%"+queryDto.Title+"%")
	}

	if queryDto.IsDelete != 0 {
		query = query.Where("is_delete = ?", queryDto.IsDelete)
	}

	return query
}

// convertToDto 将model转换为dto
func (d *popupRepo) convertToDto(popup *model.PopupModel) *dto.PopupDto {
	if popup == nil {
		return nil
	}

	return &dto.PopupDto{
		ID:         popup.ID,
		Location:   popup.Location,
		Jump:       popup.Jump,
		JumpParam:  popup.JumpParam,
		PopupImg:   popup.PopupImg,
		PopupVideo: popup.PopupVideo,
		Title:      popup.Title,
		Content:    popup.Content,
		IsDelete:   popup.IsDelete,
		CreateAt:   popup.CreateAt,
		UpdateAt:   popup.UpdateAt,
	}
}
