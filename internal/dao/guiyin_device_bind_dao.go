package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

// GuiyinDeviceBindDao 设备归因绑定数据访问层
type GuiyinDeviceBindDao struct {
	db  *gorm.DB
	log *logger.Logger
}

// NewGuiyinDeviceBindRepo 创建设备归因绑定仓库实例
func NewGuiyinDeviceBindRepo(bootStrap *component.BootStrap) repo.GuiyinDeviceBindRepo {
	return &GuiyinDeviceBindDao{
		db:  bootStrap.Driver.GetMysqlDb(),
		log: bootStrap.Log,
	}
}

// convertToDto 将model转换为dto
func (d *GuiyinDeviceBindDao) convertToDto(model *model.GuiyinDeviceBind) *dto.GuiyinDeviceBindDto {
	if model == nil {
		return nil
	}

	result := &dto.GuiyinDeviceBindDto{
		Id:        model.Id,
		DeviceId:  model.DeviceId,
		AllData:   model.AllData,
		CreateAt:  model.CreateAt,
		UpdateAt:  model.UpdateAt,
		ChannelId: model.ChannelId,
	}

	// 添加渠道信息
	if model.Channel.ID > 0 {
		result.Channel = dto.MarketingChannelDto{
			ID:        model.Channel.ID,
			Type:      model.Channel.Type,
			Title:     model.Channel.Title,
			BindKey:   model.Channel.BindKey,
			BindValue: model.Channel.BindValue,
			IsDeleted: model.Channel.IsDeleted,
			CreateAt:  model.Channel.CreateAt,
			UpdateAt:  model.Channel.UpdateAt,
		}
	}

	return result
}

// convertFromCreateDto 将创建dto转换为model
func (d *GuiyinDeviceBindDao) convertFromCreateDto(createDto *dto.GuiyinDeviceBindCreateDto) *model.GuiyinDeviceBind {
	if createDto == nil {
		return nil
	}
	return &model.GuiyinDeviceBind{
		DeviceId:  createDto.DeviceId,
		AllData:   createDto.AllData,
		ChannelId: createDto.ChannelId,
	}
}

// GetGuiyinDeviceBindByID 根据ID获取设备绑定信息
func (d *GuiyinDeviceBindDao) GetGuiyinDeviceBindByID(id int) (*dto.GuiyinDeviceBindDto, error) {
	var deviceBind model.GuiyinDeviceBind
	err := d.db.Where("id = ?", id).First(&deviceBind).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据ID获取设备绑定信息失败: %v", err)
		return nil, err
	}
	return d.convertToDto(&deviceBind), nil
}

// GetGuiyinDeviceBindByDeviceID 根据设备ID获取设备绑定信息
func (d *GuiyinDeviceBindDao) GetGuiyinDeviceBindByDeviceID(deviceId string) (*dto.GuiyinDeviceBindDto, error) {
	var deviceBind model.GuiyinDeviceBind
	err := d.db.Where("device_id = ?", deviceId).First(&deviceBind).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据设备ID获取设备绑定信息失败: %v", err)
		return nil, err
	}
	return d.convertToDto(&deviceBind), nil
}

// GetGuiyinDeviceBindList 获取设备绑定列表，支持多条件查询和分页
func (d *GuiyinDeviceBindDao) GetGuiyinDeviceBindList(queryDto *dto.GuiyinDeviceBindDto, page, pageSize int) ([]*dto.GuiyinDeviceBindDto, error) {
	var deviceBinds []model.GuiyinDeviceBind
	query := d.db.Model(&model.GuiyinDeviceBind{}).Preload("Channel")

	// 构建查询条件
	if queryDto != nil {
		if queryDto.Id > 0 {
			query = query.Where("id = ?", queryDto.Id)
		}
		if queryDto.DeviceId != "" {
			query = query.Where("device_id = ?", queryDto.DeviceId)
		}
		if queryDto.ChannelId > 0 {
			query = query.Where("channel_id = ?", queryDto.ChannelId)
		}
	}

	// 分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 排序
	query = query.Order("create_at DESC")

	err := query.Find(&deviceBinds).Error
	if err != nil {
		d.log.Error("获取设备绑定列表失败: %v", err)
		return nil, err
	}

	var result []*dto.GuiyinDeviceBindDto
	for _, deviceBind := range deviceBinds {
		result = append(result, d.convertToDto(&deviceBind))
	}

	return result, nil
}

// GetGuiyinDeviceBindCount 获取设备绑定数量，支持多条件查询
func (d *GuiyinDeviceBindDao) GetGuiyinDeviceBindCount(queryDto *dto.GuiyinDeviceBindDto) (int64, error) {
	var count int64
	query := d.db.Model(&model.GuiyinDeviceBind{})

	// 构建查询条件
	if queryDto != nil {
		if queryDto.Id > 0 {
			query = query.Where("id = ?", queryDto.Id)
		}
		if queryDto.DeviceId != "" {
			query = query.Where("device_id = ?", queryDto.DeviceId)
		}
		if queryDto.ChannelId > 0 {
			query = query.Where("channel_id = ?", queryDto.ChannelId)
		}
	}

	err := query.Count(&count).Error
	if err != nil {
		d.log.Error("获取设备绑定数量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// GetGuiyinDeviceBindsByChannelID 根据渠道ID获取设备绑定列表
func (d *GuiyinDeviceBindDao) GetGuiyinDeviceBindsByChannelID(channelId int, page, pageSize int) ([]*dto.GuiyinDeviceBindDto, error) {
	var deviceBinds []model.GuiyinDeviceBind
	query := d.db.Model(&model.GuiyinDeviceBind{}).Where("channel_id = ?", channelId)

	// 分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 排序
	query = query.Order("create_at DESC")

	err := query.Find(&deviceBinds).Error
	if err != nil {
		d.log.Error("根据渠道ID获取设备绑定列表失败: %v", err)
		return nil, err
	}

	var result []*dto.GuiyinDeviceBindDto
	for _, deviceBind := range deviceBinds {
		result = append(result, d.convertToDto(&deviceBind))
	}

	return result, nil
}

// CreateGuiyinDeviceBind 创建设备绑定
func (d *GuiyinDeviceBindDao) CreateGuiyinDeviceBind(createDto *dto.GuiyinDeviceBindCreateDto) (*dto.GuiyinDeviceBindDto, error) {
	deviceBind := d.convertFromCreateDto(createDto)

	err := d.db.Create(deviceBind).Error
	if err != nil {
		d.log.Error("创建设备绑定失败: %v", err)
		return nil, err
	}

	return d.convertToDto(deviceBind), nil
}

// UpdateGuiyinDeviceBind 更新设备绑定
func (d *GuiyinDeviceBindDao) UpdateGuiyinDeviceBind(updateDto *dto.GuiyinDeviceBindUpdateDto) (*dto.GuiyinDeviceBindDto, error) {
	// 先查询是否存在
	var existingBind model.GuiyinDeviceBind
	err := d.db.Where("id = ?", updateDto.Id).First(&existingBind).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("设备绑定不存在")
		}
		d.log.Error("查询设备绑定失败: %v", err)
		return nil, err
	}

	// 更新字段
	updates := map[string]interface{}{
		"device_id":  updateDto.DeviceId,
		"all_data":   updateDto.AllData,
		"channel_id": updateDto.ChannelId,
	}

	err = d.db.Model(&existingBind).Updates(updates).Error
	if err != nil {
		d.log.Error("更新设备绑定失败: %v", err)
		return nil, err
	}

	// 重新查询更新后的数据
	err = d.db.Where("id = ?", updateDto.Id).First(&existingBind).Error
	if err != nil {
		d.log.Error("查询更新后的设备绑定失败: %v", err)
		return nil, err
	}

	return d.convertToDto(&existingBind), nil
}

// DeleteGuiyinDeviceBind 删除设备绑定
func (d *GuiyinDeviceBindDao) DeleteGuiyinDeviceBind(id int) error {
	err := d.db.Where("id = ?", id).Delete(&model.GuiyinDeviceBind{}).Error
	if err != nil {
		d.log.Error("删除设备绑定失败: %v", err)
		return err
	}
	return nil
}
