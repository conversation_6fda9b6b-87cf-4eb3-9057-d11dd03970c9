package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"chongli/pkg/utils"
	"errors"
	"gorm.io/gorm"
	"time"
)

type templateCategoryRepo struct {
	log *logger.Logger
	db  *gorm.DB
}

func NewTemplateCategoryRepo(bootStrap *component.BootStrap) repo.TemplateCategoryRepo {
	return &templateCategoryRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
	}
}

func (d *templateCategoryRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

// convertModel2Dto 转换模型为DTO
func (d *templateCategoryRepo) convertModel2Dto(category *model.TemplateCategory) *dto.TemplateCategoryDto {
	return &dto.TemplateCategoryDto{
		ID:            category.ID,
		Name:          category.Name,
		Sort:          category.Sort,
		MaxVersion:    category.MaxVersion,
		MaxVersionInt: category.MaxVersionInt,
		MainClass:     category.MainClass,
		IsActive:      category.IsActive,
		IsDelete:      category.IsDelete,
		CreateAt:      category.CreateAt,
		UpdateAt:      category.UpdateAt,
	}
}

// CreateTemplateCategory 创建模板分类
func (d *templateCategoryRepo) CreateTemplateCategory(req *dto.CreateTemplateCategoryRequest, tx ...*gorm.DB) (*dto.TemplateCategoryDto, error) {
	now := time.Now()

	category := &model.TemplateCategory{
		Name:          req.Name,
		Sort:          req.Sort,
		MaxVersion:    req.MaxVersion,
		MaxVersionInt: utils.VersionToVersionInt(req.MaxVersion),
		MainClass:     req.MainClass,
		IsActive:      int8(model.StatusEnabled),
		IsDelete:      int8(model.StatusDisabled),
		CreateAt:      now,
		UpdateAt:      now,
	}

	if err := d.getDb(tx).Model(&model.TemplateCategory{}).Create(category).Error; err != nil {
		d.log.Error("创建模板分类错误: %v", err.Error())
		return nil, err
	}

	return d.convertModel2Dto(category), nil
}

func (d *templateCategoryRepo) GetAllCategory(tx ...*gorm.DB) ([]*dto.TemplateCategoryDto, error) {
	var categories []*model.TemplateCategory

	if err := d.getDb(tx).Model(&model.TemplateCategory{}).Where("is_delete = ?", model.StatusDisabled).Order("sort ASC, create_at DESC").Find(&categories).Error; err != nil {
		d.log.Error("查询所有模板分类失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.TemplateCategoryDto
	for _, category := range categories {
		dtoList = append(dtoList, d.convertModel2Dto(category))
	}

	return dtoList, nil
}

// GetTemplateCategoryById 根据ID查询模板分类
func (d *templateCategoryRepo) GetTemplateCategoryById(id int64, tx ...*gorm.DB) (*dto.TemplateCategoryDto, error) {
	var category model.TemplateCategory
	if err := d.getDb(tx).Model(&model.TemplateCategory{}).Where("id = ?", id).First(&category).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("查询模板分类错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&category), nil
}

// UpdateTemplateCategory 更新模板分类
func (d *templateCategoryRepo) UpdateTemplateCategory(req *dto.UpdateTemplateCategoryRequest, tx ...*gorm.DB) error {
	if req.ID == 0 {
		return errors.New("模板分类ID不能为空")
	}

	var updates = make(map[string]any)

	if req.Name != "" {
		updates["name"] = req.Name
	}

	if req.Sort != 0 {
		updates["sort"] = req.Sort
	}

	if req.MaxVersion != "" {
		updates["max_version"] = req.MaxVersion
		updates["max_version_int"] = utils.VersionToVersionInt(req.MaxVersion)
	}

	if req.MainClass != 0 {
		updates["main_class"] = req.MainClass
	}

	if req.IsActive != 0 {
		updates["is_active"] = req.IsActive
	}

	if req.IsDelete != 0 {
		updates["is_delete"] = req.IsDelete
	}

	if err := d.getDb(tx).Model(&model.TemplateCategory{}).Where("id = ?", req.ID).Updates(updates).Error; err != nil {
		d.log.Error("更新模板分类错误: %v", err.Error())
		return err
	}
	return nil
}

// DeleteTemplateCategory 删除模板分类（软删除）
func (d *templateCategoryRepo) DeleteTemplateCategory(id int64, tx ...*gorm.DB) error {
	updates := map[string]any{
		"is_delete": model.StatusEnabled,
	}

	if err := d.getDb(tx).Model(&model.TemplateCategory{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		d.log.Error("删除模板分类错误: %v", err.Error())
		return err
	}
	return nil
}

// GetTemplateCategoryByName 根据名称查询模板分类
func (d *templateCategoryRepo) GetTemplateCategoryByName(name string, tx ...*gorm.DB) (*dto.TemplateCategoryDto, error) {
	var category model.TemplateCategory
	if err := d.getDb(tx).Model(&model.TemplateCategory{}).Where(map[string]any{
		"name":      name,
		"is_delete": model.StatusDisabled,
	}).First(&category).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据名称查询模板分类错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&category), nil
}

// PageTemplateCategory 分页查询模板分类
func (d *templateCategoryRepo) PageTemplateCategory(req *dto.TemplateCategoryPageRequest) (*dto.TemplateCategoryPageResponse, error) {
	var categories []*model.TemplateCategory
	var total int64

	query := d.db.Model(&model.TemplateCategory{})

	// 添加查询条件
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}

	if req.IsActive != 0 {
		query = query.Where("is_active = ?", req.IsActive)
	}

	if req.IsDelete != 0 {
		query = query.Where("is_delete = ?", req.IsDelete)
	}

	if req.MaxVersion != "" {
		query = query.Where("max_version = ?", req.MaxVersion)
	}

	if req.MainClass != 0 {
		query = query.Where("main_class = ?", req.MainClass)
	}

	if !req.BeginAt.IsZero() {
		query = query.Where("create_at >= ?", req.BeginAt)
	}

	if !req.EndAt.IsZero() {
		query = query.Where("create_at <= ?", req.EndAt)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		d.log.Error("查询模板分类总数失败: %v", err)
		return nil, err
	}

	// 获取列表
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("sort ASC, create_at DESC").Find(&categories).Error; err != nil {
		d.log.Error("查询模板分类列表失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.TemplateCategoryDto
	for _, category := range categories {
		dtoList = append(dtoList, d.convertModel2Dto(category))
	}

	return &dto.TemplateCategoryPageResponse{
		Total: total,
		List:  dtoList,
	}, nil
}

// ListTemplateCategory 列表查询模板分类
func (d *templateCategoryRepo) ListTemplateCategory(version string, mainClassId int64) ([]*dto.TemplateCategoryDto, error) {
	var categories []*model.TemplateCategory

	// 默认只查询未删除和已启用的记录
	query := d.db.Model(&model.TemplateCategory{}).
		Where("main_class = ?", mainClassId).
		Where("is_delete = ?", model.StatusDisabled).
		Where("is_active = ?", model.StatusEnabled)

	// 如果提供了版本号，则查询max_version_int大于等于该版本的int值的模板分类
	if version != "" {
		versionInt := utils.VersionToVersionInt(version)
		query = query.Where("max_version_int >= ?", versionInt)
	}

	// 按排序和创建时间排序
	if err := query.Order("sort ASC, create_at DESC").Find(&categories).Error; err != nil {
		d.log.Error("查询模板分类列表失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.TemplateCategoryDto
	for _, category := range categories {
		dtoList = append(dtoList, d.convertModel2Dto(category))
	}

	return dtoList, nil
}

// UpdateTemplateCategoryStatus 更新模板分类状态
func (d *templateCategoryRepo) UpdateTemplateCategoryStatus(id int64, isActive int8, tx ...*gorm.DB) error {
	updates := map[string]any{
		"is_active": isActive,
	}

	if err := d.getDb(tx).Model(&model.TemplateCategory{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		d.log.Error("更新模板分类状态错误: %v", err.Error())
		return err
	}
	return nil
}

// BatchDeleteTemplateCategory 批量删除模板分类
func (d *templateCategoryRepo) BatchDeleteTemplateCategory(ids []int64, tx ...*gorm.DB) error {
	updates := map[string]any{
		"is_delete": model.StatusEnabled,
	}

	if err := d.getDb(tx).Model(&model.TemplateCategory{}).Where("id IN (?)", ids).Updates(updates).Error; err != nil {
		d.log.Error("批量删除模板分类错误: %v", err.Error())
		return err
	}
	return nil
}

// GetAllActiveTemplateCategories 获取所有启用的模板分类
func (d *templateCategoryRepo) GetAllActiveTemplateCategories(tx ...*gorm.DB) ([]*dto.TemplateCategoryDto, error) {
	var categories []*model.TemplateCategory

	if err := d.getDb(tx).Model(&model.TemplateCategory{}).Where(map[string]any{
		"is_active": model.StatusEnabled,
		"is_delete": model.StatusDisabled,
	}).Order("sort ASC, create_at DESC").Find(&categories).Error; err != nil {
		d.log.Error("查询所有启用的模板分类失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.TemplateCategoryDto
	for _, category := range categories {
		dtoList = append(dtoList, d.convertModel2Dto(category))
	}

	return dtoList, nil
}

// BatchUpdateTemplateCategory 批量更新模板分类
func (d *templateCategoryRepo) BatchUpdateTemplateCategory(req *dto.BatchUpdateTemplateCategoryRequest, tx ...*gorm.DB) error {
	if len(req.IDs) == 0 {
		return errors.New("模板分类ID列表不能为空")
	}

	var updates = make(map[string]any)

	if req.IsActive != 0 {
		updates["is_active"] = req.IsActive
	}

	if req.IsDelete != 0 {
		updates["is_delete"] = req.IsDelete
	}

	if req.MaxVersion != "" {
		updates["max_version"] = req.MaxVersion
		updates["max_version_int"] = utils.VersionToVersionInt(req.MaxVersion)
	}

	if req.MainClass != 0 {
		updates["main_class"] = req.MainClass
	}

	if len(updates) == 0 {
		return nil
	}

	if err := d.getDb(tx).Model(&model.TemplateCategory{}).Where("id IN (?)", req.IDs).Updates(updates).Error; err != nil {
		d.log.Error("批量更新模板分类错误: %v", err.Error())
		return err
	}
	return nil
}
