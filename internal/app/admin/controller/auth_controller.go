package controller

import (
	"chongli/component"
	"chongli/component/apollo"
	"chongli/internal/app/admin/dto"
	"chongli/internal/dao"
	"chongli/internal/model"
	"chongli/pkg/email"
	"chongli/pkg/jwt"
	"chongli/pkg/logger"
	"fmt"
	"math/rand"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type AuthController struct {
	log          *logger.Logger
	adminUserDao *dao.AdminUserDao
}

func NewAuthController(bootStrap *component.BootStrap) *AuthController {
	return &AuthController{
		log:          bootStrap.Log,
		adminUserDao: dao.NewAdminUserDao(bootStrap),
	}
}

// SendVerifyCode 发送邮箱验证码
func (c *AuthController) SendVerifyCode(ctx *gin.Context) {
	var req dto.AdminSendCodeRequestDto
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 检查用户是否存在
	condition := map[string]any{
		"email":     req.Email,
		"is_delete": model.StatusDisabled,
	}
	user, err := c.adminUserDao.GetAdminUserByCondition(condition)
	if err != nil {
		c.log.Error("查询用户失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if user == nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "用户不存在",
		})
		return
	}

	// 检查账户是否被锁定
	lockKey := model.RedisKeyLoginLock + req.Email
	locked, err := c.adminUserDao.ExistsRedis(lockKey)
	if err != nil {
		c.log.Error("检查账户锁定状态失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if locked {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    429,
			"message": fmt.Sprintf("账户已被锁定%d分钟，请稍后再试", model.LoginLockMinutes),
		})
		return
	}

	// 检查发送冷却时间
	cooldownKey := model.RedisKeySendCooldown + req.Email
	inCooldown, err := c.adminUserDao.ExistsRedis(cooldownKey)
	if err != nil {
		c.log.Error("检查发送冷却时间失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if inCooldown {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    429,
			"message": fmt.Sprintf("请等待%d秒后再次发送", model.SendCodeCooldownSec),
		})
		return
	}

	// 生成验证码
	code := fmt.Sprintf("%06d", rand.Intn(1000000))
	verifyCode := &model.EmailVerifyCode{
		Email:     req.Email,
		Code:      code,
		Purpose:   req.Purpose,
		ExpiredAt: time.Now().Add(time.Duration(model.VerifyCodeExpireMin) * time.Minute),
		IsUsed:    model.StatusDisabled,
		ClientIP:  c.getClientIP(ctx),
	}

	err = c.adminUserDao.CreateEmailVerifyCode(verifyCode)
	if err != nil {
		c.log.Error("生成验证码失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	// 发送邮件
	err = c.sendVerifyCodeEmail(req.Email, code, req.Purpose)
	if err != nil {
		c.log.Error("发送验证码邮件失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "发送邮件失败",
		})
		return
	}

	// 设置发送冷却时间
	err = c.adminUserDao.SetRedis(cooldownKey, 1, time.Duration(model.SendCodeCooldownSec)*time.Second)
	if err != nil {
		c.log.Error("设置发送冷却时间失败: %v", err)
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "验证码发送成功",
		"data": dto.AdminSendCodeResponseDto{
			Message:   "验证码已发送到您的邮箱，5分钟内有效",
			ExpiresAt: verifyCode.ExpiredAt,
		},
	})
}

// VerifyCode 验证邮箱验证码并登录
func (c *AuthController) VerifyCode(ctx *gin.Context) {
	var req dto.AdminLoginRequestDto
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 检查账户是否被锁定
	lockKey := model.RedisKeyLoginLock + req.Email
	locked, err := c.adminUserDao.ExistsRedis(lockKey)
	if err != nil {
		c.log.Error("检查账户锁定状态失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if locked {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    429,
			"message": fmt.Sprintf("账户已被锁定%d分钟，请稍后再试", model.LoginLockMinutes),
		})
		return
	}

	// 验证验证码 - 由于GORM的限制，时间比较需要特殊处理
	verifyCode, err := c.adminUserDao.GetEmailVerifyCodeByCondition(map[string]interface{}{
		"email":   req.Email,
		"code":    req.Code,
		"purpose": model.VerifyCodePurposeLogin,
		"is_used": model.StatusDisabled,
	})

	if err != nil || verifyCode == nil || verifyCode.ExpiredAt.Before(time.Now()) {
		// 增加登录失败次数
		attemptsKey := model.RedisKeyLoginAttempts + req.Email
		attempts, _ := c.adminUserDao.IncrRedis(attemptsKey)
		_ = c.adminUserDao.ExpireRedis(attemptsKey, time.Duration(model.LoginLockMinutes)*time.Minute)

		// 检查是否需要锁定账户
		if attempts >= int64(model.MaxLoginAttempts) {
			_ = c.adminUserDao.SetRedis(lockKey, 1, time.Duration(model.LoginLockMinutes)*time.Minute)
		}

		// 记录登录日志
		c.createLoginLog(req.Email, c.getClientIP(ctx), ctx.GetHeader("User-Agent"), model.StatusDisabled, "验证码错误或已过期")

		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "验证码错误或已过期",
		})
		return
	}

	// 标记验证码为已使用
	err = c.adminUserDao.UpdateEmailVerifyCodeByCondition(
		map[string]interface{}{"id": verifyCode.ID},
		map[string]interface{}{"is_used": model.StatusEnabled},
	)
	if err != nil {
		c.log.Error("标记验证码为已使用失败: %v", err)
	}

	// 获取用户信息
	userCondition := map[string]interface{}{
		"email":     req.Email,
		"is_delete": model.StatusDisabled,
	}
	user, err := c.adminUserDao.GetAdminUserByCondition(userCondition)
	if err != nil {
		c.log.Error("获取用户信息失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if user == nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "用户不存在",
		})
		return
	}

	// 更新用户登录信息
	clientIP := c.getClientIP(ctx)
	loginUpdates := map[string]interface{}{
		"last_login_time": time.Now(),
		"last_login_ip":   clientIP,
		"login_count":     user.LoginCount + 1,
	}

	err = c.adminUserDao.UpdateAdminUserByCondition(
		map[string]interface{}{"id": user.ID},
		loginUpdates,
	)
	if err != nil {
		c.log.Error("更新用户登录信息失败: %v", err)
	}

	// 清除登录失败次数
	attemptsKey := model.RedisKeyLoginAttempts + req.Email
	err = c.adminUserDao.DeleteRedis(attemptsKey)
	if err != nil {
		c.log.Error("清除登录失败次数失败: %v", err)
	}

	// 记录登录日志
	c.createLoginLog(req.Email, clientIP, ctx.GetHeader("User-Agent"), model.StatusEnabled, "登录成功")

	// 生成JWT Token
	jwtSecret := apollo.GetApolloConfig().JwtSecret
	meta := map[string]interface{}{
		"user_id":   user.ID,
		"email":     user.Email,
		"user_type": "admin",
	}
	token, err := jwt.GenerateToken(jwtSecret, time.Hour*24*7, meta) // 7天有效期
	if err != nil {
		c.log.Error("生成JWT Token失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "生成Token失败",
		})
		return
	}

	// 转换为DTO
	userDto := c.adminUserDao.ConvertToAdminUserDto(user)

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "登录成功",
		"data": dto.AdminLoginResponseDto{
			Token:    token,
			UserInfo: userDto,
		},
	})
}

// sendVerifyCodeEmail 发送验证码邮件
func (c *AuthController) sendVerifyCodeEmail(emailAddr, code string, purpose int8) error {
	var subject, content string

	switch purpose {
	case model.VerifyCodePurposeLogin:
		subject = "后台管理系统登录验证码"
		content = fmt.Sprintf("您的登录验证码是：%s，有效期5分钟，请勿泄露给他人。", code)
	case model.VerifyCodePurposeResetPwd:
		subject = "后台管理系统密码重置验证码"
		content = fmt.Sprintf("您的密码重置验证码是：%s，有效期5分钟，请勿泄露给他人。", code)
	default:
		subject = "后台管理系统验证码"
		content = fmt.Sprintf("您的验证码是：%s，有效期5分钟，请勿泄露给他人。", code)
	}

	sendObject := email.SendObject{
		Subject: subject,
		Content: content,
		To:      emailAddr,
	}

	return email.SendEmail(sendObject, email.NormalEmail)
}

// createLoginLog 创建登录日志
func (c *AuthController) createLoginLog(emailAddr, loginIP, userAgent string, status model.StatusFlag, message string) {
	// 获取用户ID
	var userID int64 = 0
	userCondition := map[string]interface{}{
		"email":     emailAddr,
		"is_delete": model.StatusDisabled,
	}
	user, err := c.adminUserDao.GetAdminUserByCondition(userCondition)
	if err == nil && user != nil {
		userID = user.ID
	}

	loginLog := &model.AdminLoginLog{
		UserID:    userID,
		Email:     emailAddr,
		LoginIP:   loginIP,
		UserAgent: userAgent,
		Status:    status,
		Message:   message,
	}

	err = c.adminUserDao.CreateAdminLoginLog(loginLog)
	if err != nil {
		c.log.Error("创建登录日志失败: %v", err)
	}
}

// getClientIP 获取客户端IP地址
func (c *AuthController) getClientIP(ctx *gin.Context) string {
	// 尝试从各种头部获取真实IP
	ip := ctx.GetHeader("X-Forwarded-For")
	if ip != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		ips := strings.Split(ip, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	ip = ctx.GetHeader("X-Real-IP")
	if ip != "" {
		return ip
	}

	ip = ctx.GetHeader("X-Original-Forwarded-For")
	if ip != "" {
		return ip
	}

	// 最后使用RemoteAddr
	return ctx.ClientIP()
}
