package controller

import (
	"chongli/component"
	"chongli/component/driver"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"encoding/json"
	"github.com/gin-gonic/gin"
)

type TemplateManageController struct {
	log                  *logger.Logger
	tx                   driver.ITransaction
	templateCategoryRepo repo.TemplateCategoryRepo
	configRepo           repo.ConfigRepo
}

func NewTemplateManageController(
	bootStrap *component.BootStrap,
	templateCategoryRepo repo.TemplateCategoryRepo,
	configRepo repo.ConfigRepo,
) *TemplateManageController {
	return &TemplateManageController{
		log:                  bootStrap.Log,
		tx:                   bootStrap.Tx,
		templateCategoryRepo: templateCategoryRepo,
		configRepo:           configRepo,
	}
}

// GetTemplateCategoryList 分页条件查询模板分类
func (c *TemplateManageController) GetTemplateCategoryList(ctx *gin.Context) {
	var req dto.TemplateCategoryPageRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	resp, err := c.templateCategoryRepo.PageTemplateCategory(&req)
	if err != nil {
		c.log.Error("分页查询模板分类失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, resp, nil, response.WithSLSLog)
}

// CreateTemplateCategory 创建模板分类
func (c *TemplateManageController) CreateTemplateCategory(ctx *gin.Context) {
	var req dto.CreateTemplateCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	// 检查分类名称是否已存在
	existCategory, err := c.templateCategoryRepo.GetTemplateCategoryByName(req.Name)
	if err != nil {
		c.log.Error("检查模板分类名称失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}
	if existCategory != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("分类名称已存在"), response.WithSLSLog)
		return
	}

	mysqlTx := c.tx.MysqlDbTxBegin()
	category, err := c.templateCategoryRepo.CreateTemplateCategory(&req, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		c.log.Error("创建模板分类失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		c.log.Error("提交事务失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, category, nil, response.WithSLSLog)
}

// UpdateTemplateCategory 更新模板分类
func (c *TemplateManageController) UpdateTemplateCategory(ctx *gin.Context) {
	var req dto.UpdateTemplateCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	// 检查分类是否存在
	existCategory, err := c.templateCategoryRepo.GetTemplateCategoryById(req.ID)
	if err != nil {
		c.log.Error("查询模板分类失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}
	if existCategory == nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("模板分类不存在"), response.WithSLSLog)
		return
	}

	// 如果更新名称，检查名称是否已被其他分类使用
	if req.Name != "" && req.Name != existCategory.Name {
		nameCategory, err := c.templateCategoryRepo.GetTemplateCategoryByName(req.Name)
		if err != nil {
			c.log.Error("检查模板分类名称失败: %v", err)
			response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
			return
		}
		if nameCategory != nil && nameCategory.ID != req.ID {
			response.Response(ctx, nil, nil, errpkg.NewLowError("分类名称已存在"), response.WithSLSLog)
			return
		}
	}

	mysqlTx := c.tx.MysqlDbTxBegin()
	err = c.templateCategoryRepo.UpdateTemplateCategory(&req, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		c.log.Error("更新模板分类失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		c.log.Error("提交事务失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}

// BatchDeleteTemplateCategory 批量删除模板分类
func (c *TemplateManageController) BatchDeleteTemplateCategory(ctx *gin.Context) {
	var req dto.BatchDeleteTemplateCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	mysqlTx := c.tx.MysqlDbTxBegin()
	err := c.templateCategoryRepo.BatchDeleteTemplateCategory(req.IDs, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		c.log.Error("批量删除模板分类失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		c.log.Error("提交事务失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}

// BatchUpdateTemplateCategory 批量更新模板分类
func (c *TemplateManageController) BatchUpdateTemplateCategory(ctx *gin.Context) {
	var req dto.BatchUpdateTemplateCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	mysqlTx := c.tx.MysqlDbTxBegin()
	err := c.templateCategoryRepo.BatchUpdateTemplateCategory(&req, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		c.log.Error("批量更新模板分类失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		c.log.Error("提交事务失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}

func (c *TemplateManageController) GetMainClassList(ctx *gin.Context) {
	// 从配置获取主分类列表
	config, err := c.configRepo.GetConfigByKey("app_main_class")
	if err != nil {
		c.log.Error("获取主分类配置失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	if config == nil || config.ConfigValue == "" {
		response.Response(ctx, nil, nil, errpkg.NewLowError("主分类配置不存在或未设置"), response.WithSLSLog)
		return
	}

	var mainClasses []*dto.AppMainClassConfigDto
	if err := json.Unmarshal([]byte(config.ConfigValue), &mainClasses); err != nil {
		c.log.Error("解析主分类配置失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError("解析主分类配置失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, mainClasses, nil, response.WithSLSLog)
}

func (c *TemplateManageController) GetAllCategories(ctx *gin.Context) {
	categories, err := c.templateCategoryRepo.GetAllCategory()
	if err != nil {
		c.log.Error("获取所有模板分类失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, categories, nil, response.WithSLSLog)
}
