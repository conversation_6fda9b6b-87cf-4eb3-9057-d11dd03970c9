package controller

import (
	"chongli/component"
	"chongli/internal/dao"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// PopupController 弹窗控制器
type PopupController struct {
	log  *logger.Logger
	repo repo.PopupRepo
}

// NewPopupController 创建弹窗控制器实例
func NewPopupController(bootStrap *component.BootStrap) *PopupController {
	return &PopupController{
		log:  bootStrap.Log,
		repo: dao.NewPopupRepo(bootStrap),
	}
}

// PopupList 获取弹窗列表
func (c *PopupController) PopupList(ctx *gin.Context) {
	var req dto.PopupQueryDto
	if err := ctx.ShouldBindQuery(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取列表
	popups, err := c.repo.GetPopupList(&req)
	if err != nil {
		c.log.Error("获取弹窗列表失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	// 获取总数
	total, err := c.repo.GetPopupCount(&req)
	if err != nil {
		c.log.Error("获取弹窗总数失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      popups,
			"count":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// PopupDetail 获取弹窗详情
func (c *PopupController) PopupDetail(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "无效的弹窗ID",
		})
		return
	}

	// 获取弹窗信息
	popup, err := c.repo.GetPopupByID(id)
	if err != nil {
		c.log.Error("获取弹窗详情失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if popup == nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    404,
			"message": "弹窗不存在",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    popup,
	})
}

// PopupAdd 创建弹窗
func (c *PopupController) PopupAdd(ctx *gin.Context) {
	var req dto.PopupCreateDto
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 创建弹窗
	popup, err := c.repo.CreatePopup(&req)
	if err != nil {
		c.log.Error("创建弹窗失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    popup,
	})
}

// PopupUpdate 更新弹窗
func (c *PopupController) PopupUpdate(ctx *gin.Context) {
	var req dto.PopupUpdateDto
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 更新弹窗
	popup, err := c.repo.UpdatePopup(&req)
	if err != nil {
		c.log.Error("更新弹窗失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    popup,
	})
}

// PopupDelete 删除弹窗（软删除）
func (c *PopupController) PopupDelete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "无效的弹窗ID",
		})
		return
	}

	// 检查弹窗是否存在
	popup, err := c.repo.GetPopupByID(id)
	if err != nil {
		c.log.Error("获取弹窗信息失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if popup == nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    404,
			"message": "弹窗不存在",
		})
		return
	}

	// 执行软删除
	err = c.repo.DeletePopup(id)
	if err != nil {
		c.log.Error("删除弹窗失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// GetPopupByLocation 根据位置获取弹窗
func (c *PopupController) GetPopupByLocation(ctx *gin.Context) {
	location := ctx.Param("location")
	if location == "" {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "弹窗位置不能为空",
		})
		return
	}

	// 获取弹窗列表
	popups, err := c.repo.GetPopupByLocation(location)
	if err != nil {
		c.log.Error("根据位置获取弹窗失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    popups,
	})
}
