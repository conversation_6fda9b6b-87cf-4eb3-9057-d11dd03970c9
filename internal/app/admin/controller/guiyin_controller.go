package controller

import (
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type GuiYinController struct {
	guiyinDeviceBindRepo repo.GuiyinDeviceBindRepo
	guiyinService        *service.GuiyinService
}

// 获取列表请求参数
type GuiYinListRequest struct {
	Id        int    `form:"id" json:"id"`
	DeviceId  string `form:"device_id" json:"device_id"`
	ChannelId int    `form:"channel_id" json:"channel_id"`
	Page      int    `form:"page,default=1" json:"page"`
	PageSize  int    `form:"page_size,default=10" json:"page_size"`
}

// 创建设备绑定请求参数
type CreateGuiYinRequest struct {
	DeviceId  string `json:"device_id" binding:"required"`
	AllData   string `json:"all_data"`
	ChannelId int    `json:"channel_id"`
}

// 更新设备绑定请求参数
type UpdateGuiYinRequest struct {
	Id        int    `json:"id" binding:"required"`
	DeviceId  string `json:"device_id"`
	AllData   string `json:"all_data"`
	ChannelId int    `json:"channel_id"`
}

func NewGuiYinController(guiyinDeviceBindRepo repo.GuiyinDeviceBindRepo, guiyinService *service.GuiyinService) *GuiYinController {
	return &GuiYinController{
		guiyinDeviceBindRepo: guiyinDeviceBindRepo,
		guiyinService:        guiyinService,
	}
}

// GetById 根据ID获取设备绑定信息
func (a *GuiYinController) GetById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		_err := errpkg.NewMiddleErrorWithCause(err, response.BadRequest)
		response.Response(c, nil, nil, _err, response.WithSLSLog)
		return
	}

	data, err := a.guiyinDeviceBindRepo.GetGuiyinDeviceBindByID(id)
	if err != nil {
		_err := errpkg.NewMiddleErrorWithCause(err, response.BadRequest)
		response.Response(c, nil, nil, _err, response.WithSLSLog)
		return
	}
	response.Response(c, nil, data, nil, response.WithSLSLog)
}

// GetByDeviceId 根据设备ID获取设备绑定信息
func (a *GuiYinController) GetByDeviceId(c *gin.Context) {
	deviceId := c.Param("device_id")
	if deviceId == "" {
		_err := errpkg.NewMiddleError("设备ID不能为空")
		response.Response(c, nil, nil, _err, response.WithSLSLog)
		return
	}

	data, err := a.guiyinDeviceBindRepo.GetGuiyinDeviceBindByDeviceID(deviceId)
	if err != nil {
		_err := errpkg.NewMiddleErrorWithCause(err, response.BadRequest)
		response.Response(c, nil, nil, _err, response.WithSLSLog)
		return
	}
	response.Response(c, nil, data, nil, response.WithSLSLog)
}

// List 获取设备绑定列表
func (a *GuiYinController) List(c *gin.Context) {
	var req GuiYinListRequest
	if errBind := c.ShouldBind(&req); errBind != nil {
		err := errpkg.NewMiddleErrorWithCause(errBind, response.BadRequest)
		response.Response(c, req, nil, err, response.WithSLSLog)
		return
	}

	// 构建查询条件
	queryDto := &dto.GuiyinDeviceBindDto{
		Id:        req.Id,
		DeviceId:  req.DeviceId,
		ChannelId: req.ChannelId,
	}

	// 获取列表数据
	data, err := a.guiyinDeviceBindRepo.GetGuiyinDeviceBindList(queryDto, req.Page, req.PageSize)
	if err != nil {
		_err := errpkg.NewMiddleErrorWithCause(err, response.BadRequest)
		response.Response(c, nil, nil, _err, response.WithSLSLog)
		return
	}

	// 获取总数量
	count, err := a.guiyinDeviceBindRepo.GetGuiyinDeviceBindCount(queryDto)
	if err != nil {
		_err := errpkg.NewMiddleErrorWithCause(err, response.BadRequest)
		response.Response(c, nil, nil, _err, response.WithSLSLog)
		return
	}

	result := map[string]interface{}{
		"list":  data,
		"count": count,
		"page":  req.Page,
		"size":  req.PageSize,
	}
	response.Response(c, nil, result, nil, response.WithSLSLog)
}

// Create 创建设备绑定
func (a *GuiYinController) Create(c *gin.Context) {
	var req CreateGuiYinRequest
	if errBind := c.ShouldBindJSON(&req); errBind != nil {
		err := errpkg.NewMiddleErrorWithCause(errBind, response.BadRequest)
		response.Response(c, req, nil, err, response.WithSLSLog)
		return
	}

	createDto := &dto.GuiyinDeviceBindCreateDto{
		DeviceId:  req.DeviceId,
		AllData:   req.AllData,
		ChannelId: req.ChannelId,
	}

	data, err := a.guiyinDeviceBindRepo.CreateGuiyinDeviceBind(createDto)
	if err != nil {
		_err := errpkg.NewMiddleErrorWithCause(err, response.BadRequest)
		response.Response(c, nil, nil, _err, response.WithSLSLog)
		return
	}
	response.Response(c, nil, data, nil, response.WithSLSLog)
}

// Update 更新设备绑定
func (a *GuiYinController) Update(c *gin.Context) {
	var req UpdateGuiYinRequest
	if errBind := c.ShouldBindJSON(&req); errBind != nil {
		err := errpkg.NewMiddleErrorWithCause(errBind, response.BadRequest)
		response.Response(c, req, nil, err, response.WithSLSLog)
		return
	}

	updateDto := &dto.GuiyinDeviceBindUpdateDto{
		Id:        req.Id,
		DeviceId:  req.DeviceId,
		AllData:   req.AllData,
		ChannelId: req.ChannelId,
	}

	data, err := a.guiyinDeviceBindRepo.UpdateGuiyinDeviceBind(updateDto)
	if err != nil {
		_err := errpkg.NewMiddleErrorWithCause(err, response.BadRequest)
		response.Response(c, nil, nil, _err, response.WithSLSLog)
		return
	}
	response.Response(c, nil, data, nil, response.WithSLSLog)
}

// Delete 删除设备绑定
func (a *GuiYinController) Delete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		_err := errpkg.NewMiddleErrorWithCause(err, response.BadRequest)
		response.Response(c, nil, nil, _err, response.WithSLSLog)
		return
	}

	err = a.guiyinDeviceBindRepo.DeleteGuiyinDeviceBind(id)
	if err != nil {
		_err := errpkg.NewMiddleErrorWithCause(err, response.BadRequest)
		response.Response(c, nil, nil, _err, response.WithSLSLog)
		return
	}
	response.Response(c, nil, map[string]interface{}{"message": "删除成功"}, nil, response.WithSLSLog)
}
