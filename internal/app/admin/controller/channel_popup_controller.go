package controller

import (
	"chongli/component"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// === 工具函数 ===

// parseCommaSeparatedIDs 解析逗号分隔的ID字符串为整数切片
func (c *ChannelPopupController) parseCommaSeparatedIDs(idStr string) ([]int, error) {
	if strings.TrimSpace(idStr) == "" {
		return nil, fmt.Errorf("ID字符串不能为空")
	}

	idStrings := strings.Split(idStr, ",")
	var ids []int

	for _, idStr := range idStrings {
		idStr = strings.TrimSpace(idStr)
		if idStr == "" {
			continue // 跳过空字符串
		}

		id, err := strconv.Atoi(idStr)
		if err != nil {
			return nil, fmt.Errorf("无效的ID格式: %s", idStr)
		}

		if id <= 0 {
			return nil, fmt.Errorf("ID必须大于0: %d", id)
		}

		ids = append(ids, id)
	}

	if len(ids) == 0 {
		return nil, fmt.Errorf("未找到有效的ID")
	}

	return ids, nil
}

// === 对外接口 DTO 定义 ===

// ChannelPopupQueryRequest 查询渠道弹窗关联请求
type ChannelPopupQueryRequest struct {
	Page             int    `json:"page" form:"page" binding:"min=1"`                   // 页码
	PageSize         int    `json:"page_size" form:"page_size" binding:"min=1,max=100"` // 每页大小
	ChannelID        int    `json:"channel_id" form:"channel_id"`                       // 渠道ID筛选
	PopupID          int    `json:"popup_id" form:"popup_id"`                           // 弹窗ID筛选
	ChannelTitle     string `json:"channel_title" form:"channel_title"`                 // 渠道标题筛选
	PopupTitle       string `json:"popup_title" form:"popup_title"`                     // 弹窗标题筛选
	ChannelType      *int8  `json:"channel_type" form:"channel_type"`                   // 渠道类型筛选
	PopupLocation    string `json:"popup_location" form:"popup_location"`               // 弹窗位置筛选
	ChannelIsDeleted *int8  `json:"channel_is_deleted" form:"channel_is_deleted"`       // 渠道删除状态筛选：-1未删除，1已删除
	PopupIsDeleted   *int8  `json:"popup_is_deleted" form:"popup_is_deleted"`           // 弹窗删除状态筛选：-1未删除，1已删除
}

// ChannelPopupBatchCreateRequest 批量创建渠道弹窗关联请求
type ChannelPopupBatchCreateRequest struct {
	ChannelIDs string `json:"channel_ids" binding:"required"` // 渠道ID列表，逗号分隔
	PopupIDs   string `json:"popup_ids" binding:"required"`   // 弹窗ID列表，逗号分隔
}

// ChannelPopupDeleteRequest 删除渠道弹窗关联请求
type ChannelPopupDeleteRequest struct {
	ID int `json:"id" binding:"required,min=1"` // 主键ID
}

// ChannelPopupBatchDeleteRequest 批量删除渠道弹窗关联请求
type ChannelPopupBatchDeleteRequest struct {
	Ids string `json:"ids" binding:"required"` // 主键ID列表，逗号分隔
}

// ChannelPopupResponse 渠道弹窗关联响应
type ChannelPopupResponse struct {
	ID        int                      `json:"id"`         // 主键ID
	ChannelID int                      `json:"channel_id"` // 渠道ID
	PopupID   int                      `json:"popup_id"`   // 弹窗ID
	CreateAt  time.Time                `json:"create_at"`  // 创建时间
	UpdateAt  time.Time                `json:"update_at"`  // 更新时间
	Channel   *dto.MarketingChannelDto `json:"channel"`    // 渠道详细信息
	Popup     *dto.PopupDto            `json:"popup"`      // 弹窗详细信息
}

// === 转换函数 ===

// convertToInternalQuery 将外部查询请求转换为内部 DTO
func (c *ChannelPopupController) convertToInternalQuery(req *ChannelPopupQueryRequest) *dto.ChannelPopupQueryDto {
	return &dto.ChannelPopupQueryDto{
		Page:             req.Page,
		PageSize:         req.PageSize,
		ChannelID:        req.ChannelID,
		PopupID:          req.PopupID,
		ChannelTitle:     req.ChannelTitle,
		PopupTitle:       req.PopupTitle,
		ChannelType:      req.ChannelType,
		PopupLocation:    req.PopupLocation,
		ChannelIsDeleted: req.ChannelIsDeleted,
		PopupIsDeleted:   req.PopupIsDeleted,
	}
}

// convertToResponse 将内部 DTO 转换为外部响应
func (c *ChannelPopupController) convertToResponse(internal *dto.ChannelPopupDto) *ChannelPopupResponse {
	if internal == nil {
		return nil
	}
	return &ChannelPopupResponse{
		ID:        internal.ID,
		ChannelID: internal.ChannelID,
		PopupID:   internal.PopupID,
		CreateAt:  internal.CreateAt,
		UpdateAt:  internal.UpdateAt,
		Channel:   internal.Channel,
		Popup:     internal.Popup,
	}
}

// convertToResponseList 将内部 DTO 列表转换为外部响应列表
func (c *ChannelPopupController) convertToResponseList(internalList []*dto.ChannelPopupDto) []*ChannelPopupResponse {
	var responseList []*ChannelPopupResponse
	for _, internal := range internalList {
		responseList = append(responseList, c.convertToResponse(internal))
	}
	return responseList
}

// ChannelPopupController 渠道弹窗关联控制器
type ChannelPopupController struct {
	log  *logger.Logger
	repo repo.ChannelPopupRepo
}

// NewChannelPopupController 创建渠道弹窗关联控制器实例
func NewChannelPopupController(bootStrap *component.BootStrap, repo repo.ChannelPopupRepo) *ChannelPopupController {
	return &ChannelPopupController{
		log:  bootStrap.Log,
		repo: repo,
	}
}

// ChannelPopupList 获取渠道弹窗关联列表（通用查询）
func (c *ChannelPopupController) ChannelPopupList(ctx *gin.Context) {
	var req ChannelPopupQueryRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 转换为内部 DTO
	internalReq := c.convertToInternalQuery(&req)

	// 获取带详细信息的关联列表
	relations, err := c.repo.GetChannelPopupWithDetails(internalReq)
	if err != nil {
		c.log.Error("获取渠道弹窗关联列表失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	// 获取总数
	total, err := c.repo.GetChannelPopupCount(internalReq)
	if err != nil {
		c.log.Error("获取渠道弹窗关联总数失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	// 转换为外部响应
	responseList := c.convertToResponseList(relations)

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      responseList,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// ChannelPopupAdd 批量创建渠道弹窗关联
func (c *ChannelPopupController) ChannelPopupAdd(ctx *gin.Context) {
	var req ChannelPopupBatchCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 1. 解析逗号分隔的ID字符串
	channelIDs, err := c.parseCommaSeparatedIDs(req.ChannelIDs)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "渠道ID格式错误: " + err.Error(),
		})
		return
	}

	popupIDs, err := c.parseCommaSeparatedIDs(req.PopupIDs)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "弹窗ID格式错误: " + err.Error(),
		})
		return
	}

	// 2. 生成所有可能的组合（笛卡尔积）
	var combinations []dto.ChannelPopupCreateDto
	for _, channelID := range channelIDs {
		for _, popupID := range popupIDs {
			combinations = append(combinations, dto.ChannelPopupCreateDto{
				ChannelID: channelID,
				PopupID:   popupID,
			})
		}
	}

	if len(combinations) == 0 {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    200,
			"message": "没有需要创建的关联",
			"data": gin.H{
				"total":     0,
				"relations": []interface{}{},
			},
		})
		return
	}

	// 3. 查询已存在的关联关系
	existingRelations, err := c.repo.GetExistingRelations(channelIDs, popupIDs)
	if err != nil {
		c.log.Error("查询已存在关联关系失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "查询失败: " + err.Error(),
		})
		return
	}

	// 4. 创建已存在关联的映射表，用于快速查找
	existingMap := make(map[string]bool)
	for _, existing := range existingRelations {
		key := fmt.Sprintf("%d_%d", existing.ChannelID, existing.PopupID)
		existingMap[key] = true
	}

	// 5. 筛选出需要新建的关联关系
	var newCombinations []dto.ChannelPopupCreateDto
	for _, combo := range combinations {
		key := fmt.Sprintf("%d_%d", combo.ChannelID, combo.PopupID)
		if !existingMap[key] {
			newCombinations = append(newCombinations, combo)
		}
	}

	var newRelations []*dto.ChannelPopupDto

	// 6. 批量创建新的关联关系
	if len(newCombinations) > 0 {
		newRelations, err = c.repo.BatchCreateRelations(newCombinations)
		if err != nil {
			c.log.Error("批量创建关联关系失败: %v", err)
			ctx.JSON(http.StatusOK, gin.H{
				"code":    500,
				"message": "创建失败: " + err.Error(),
			})
			return
		}
	}

	// 7. 合并已存在的和新创建的关联
	var allResults []*dto.ChannelPopupDto
	allResults = append(allResults, existingRelations...)
	allResults = append(allResults, newRelations...)

	// 转换为外部响应
	responseList := c.convertToResponseList(allResults)

	c.log.Info("批量创建渠道弹窗关联完成，总数: %d，新建: %d，已存在: %d",
		len(allResults), len(newRelations), len(existingRelations))

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data": gin.H{
			"total":       len(allResults),
			"new_created": len(newRelations),
			"existed":     len(existingRelations),
			"relations":   responseList,
		},
	})
}

// ChannelPopupDelete 删除渠道弹窗关联（硬删除）
func (c *ChannelPopupController) ChannelPopupDelete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "无效的关联ID",
		})
		return
	}

	// 删除关联
	err = c.repo.DeleteChannelPopup(id)
	if err != nil {
		c.log.Error("删除渠道弹窗关联失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// ChannelPopupBatchDelete 批量删除渠道弹窗关联
func (c *ChannelPopupController) ChannelPopupBatchDelete(ctx *gin.Context) {
	var req ChannelPopupBatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 解析逗号分隔的ID字符串
	ids, err := c.parseCommaSeparatedIDs(req.Ids)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "ID格式错误: " + err.Error(),
		})
		return
	}

	if len(ids) == 0 {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "至少需要提供一个ID",
		})
		return
	}

	// 批量删除关联
	err = c.repo.DeleteChannelPopupByIds(ids)
	if err != nil {
		c.log.Error("批量删除渠道弹窗关联失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}
