package controller

import (
	"chongli/component"
	"chongli/internal/dao"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// === 工具函数 ===

// parseCommaSeparatedIDs 解析逗号分隔的ID字符串为整数切片
func (c *ConfigChannelRelationController) parseCommaSeparatedIDs(idStr string) ([]int, error) {
	if strings.TrimSpace(idStr) == "" {
		return nil, fmt.Errorf("ID字符串不能为空")
	}

	idStrings := strings.Split(idStr, ",")
	var ids []int

	for _, idStr := range idStrings {
		idStr = strings.TrimSpace(idStr)
		if idStr == "" {
			continue // 跳过空字符串
		}

		id, err := strconv.Atoi(idStr)
		if err != nil {
			return nil, fmt.Errorf("无效的ID格式: %s", idStr)
		}

		if id <= 0 {
			return nil, fmt.Errorf("ID必须大于0: %d", id)
		}

		ids = append(ids, id)
	}

	if len(ids) == 0 {
		return nil, fmt.Errorf("未找到有效的ID")
	}

	return ids, nil
}

// === 对外接口 DTO 定义 ===

// ConfigChannelRelationQueryRequest 查询配置渠道关联请求
type ConfigChannelRelationQueryRequest struct {
	Page               int    `json:"page" form:"page" binding:"min=1"`                 // 页码
	PageSize           int    `json:"size" form:"size" binding:"min=1,max=100"`         // 每页大小
	ConfigID           int    `json:"config_id" form:"config_id"`                       // 配置ID筛选
	MarketingChannelID int    `json:"marketing_channel_id" form:"marketing_channel_id"` // 营销渠道ID筛选
	ConfigKey          string `json:"config_key" form:"config_key"`                     // 配置键筛选
	ChannelTitle       string `json:"channel_title" form:"channel_title"`               // 渠道标题筛选
	ChannelType        *int8  `json:"channel_type" form:"channel_type"`                 // 渠道类型筛选
	ChannelIsDeleted   *int8  `json:"channel_is_deleted" form:"channel_is_deleted"`     // 渠道删除状态筛选：-1未删除，1已删除
}

// ConfigChannelRelationBatchCreateRequest 批量创建配置渠道关联请求
type ConfigChannelRelationBatchCreateRequest struct {
	ConfigIDs  []int `json:"config_ids" binding:"required"`  // 配置ID列表，逗号分隔
	ChannelIDs []int `json:"channel_ids" binding:"required"` // 渠道ID列表，逗号分隔
}

// ConfigChannelRelationResponse 配置渠道关联响应
type ConfigChannelRelationResponse struct {
	ID                 int64                     `json:"id"`                   // 主键ID
	ConfigID           int                       `json:"config_id"`            // 配置ID
	MarketingChannelID int                       `json:"marketing_channel_id"` // 营销渠道ID
	CreateAt           time.Time                 `json:"create_at"`            // 创建时间
	Config             *ConfigResponse           `json:"config"`               // 配置详细信息
	MarketingChannel   *MarketingChannelResponse `json:"marketing_channel"`    // 渠道详细信息
}

// MarketingChannelResponse 营销渠道响应
type MarketingChannelResponse struct {
	ID        int       `json:"id"`         // 主键id
	Type      int8      `json:"type"`       // 类型：1-匹配商店渠道，2-匹配归因推广
	Title     string    `json:"title"`      // 标题
	BindKey   string    `json:"bind_key"`   // 绑定键
	BindValue string    `json:"bind_value"` // 绑定值
	IsDeleted int8      `json:"is_deleted"` // 是否删除：-1-未删除，1-已删除
	CreateAt  time.Time `json:"create_at"`  // 创建时间
	UpdateAt  time.Time `json:"update_at"`  // 更新时间
}

// === 转换函数 ===

// convertToInternalQuery 将外部查询请求转换为内部 DTO
func (c *ConfigChannelRelationController) convertToInternalQuery(req *ConfigChannelRelationQueryRequest) *dto.ConfigChannelRelationDto {
	return &dto.ConfigChannelRelationDto{
		Page:               req.Page,
		PageSize:           req.PageSize,
		ConfigID:           req.ConfigID,
		MarketingChannelID: req.MarketingChannelID,
	}
}

// convertToResponse 将内部 DTO 转换为外部响应
func (c *ConfigChannelRelationController) convertToResponse(internal *dto.ConfigChannelRelationDto) *ConfigChannelRelationResponse {
	if internal == nil {
		return nil
	}

	response := &ConfigChannelRelationResponse{
		ID:                 internal.ID,
		ConfigID:           internal.ConfigID,
		MarketingChannelID: internal.MarketingChannelID,
		CreateAt:           internal.CreateAt,
	}

	// 转换配置信息
	if internal.Config != nil {
		response.Config = &ConfigResponse{
			Id:          internal.Config.Id,
			ConfigKey:   internal.Config.ConfigKey,
			ConfigValue: internal.Config.ConfigValue,
			Remark:      internal.Config.Remark,
			CreateAt:    internal.Config.CreateAt,
			IsDelete:    int8(internal.Config.IsDelete),
			IsChannel:   int8(internal.Config.IsChannel),
			IsPublish:   int8(internal.Config.IsPublish),
		}
	}

	// 转换渠道信息
	if internal.MarketingChannel != nil {
		response.MarketingChannel = &MarketingChannelResponse{
			ID:        internal.MarketingChannel.ID,
			Type:      internal.MarketingChannel.Type,
			Title:     internal.MarketingChannel.Title,
			BindKey:   internal.MarketingChannel.BindKey,
			BindValue: internal.MarketingChannel.BindValue,
			IsDeleted: internal.MarketingChannel.IsDeleted,
			CreateAt:  internal.MarketingChannel.CreateAt,
			UpdateAt:  internal.MarketingChannel.UpdateAt,
		}
	}

	return response
}

// convertToResponseList 将内部 DTO 列表转换为外部响应列表
func (c *ConfigChannelRelationController) convertToResponseList(internalList []*dto.ConfigChannelRelationDto) []*ConfigChannelRelationResponse {
	var responseList []*ConfigChannelRelationResponse
	for _, internal := range internalList {
		responseList = append(responseList, c.convertToResponse(internal))
	}
	return responseList
}

// ConfigChannelRelationController 配置渠道关联控制器
type ConfigChannelRelationController struct {
	log         *logger.Logger
	repo        repo.ConfigChannelRelationRepo
	configRepo  repo.ConfigRepo
	channelRepo repo.MarketingChannelRepo
}

// NewConfigChannelRelationController 创建配置渠道关联控制器实例
func NewConfigChannelRelationController(bootStrap *component.BootStrap) *ConfigChannelRelationController {
	return &ConfigChannelRelationController{
		log:         bootStrap.Log,
		repo:        dao.NewConfigChannelRelationRepo(bootStrap),
		configRepo:  dao.NewConfigRepo(bootStrap),
		channelRepo: dao.NewMarketingChannelRepo(bootStrap),
	}
}

// ConfigChannelRelationList 获取配置渠道关联列表（通用查询）
func (c *ConfigChannelRelationController) ConfigChannelRelationList(ctx *gin.Context) {
	var req ConfigChannelRelationQueryRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 转换为内部查询DTO
	queryDto := c.convertToInternalQuery(&req)

	// 查询配置渠道关联列表
	relations, err := c.repo.List(queryDto)
	if err != nil {
		c.log.Error("查询配置渠道关联列表失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "查询失败: " + err.Error(),
		})
		return
	}

	// 转换为响应
	responseList := c.convertToResponseList(relations)

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "查询成功",
		"data": gin.H{
			"list":      responseList,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// ConfigChannelRelationAdd 批量创建配置渠道关联
func (c *ConfigChannelRelationController) ConfigChannelRelationAdd(ctx *gin.Context) {
	var req ConfigChannelRelationBatchCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 1. 解析配置ID列表
	configIDs := req.ConfigIDs

	// 2. 解析渠道ID列表
	channelIDs := req.ChannelIDs

	// 3. 生成所有可能的配置-渠道组合
	var combinations []struct {
		ConfigID  int
		ChannelID int
	}
	for _, configID := range configIDs {
		for _, channelID := range channelIDs {
			combinations = append(combinations, struct {
				ConfigID  int
				ChannelID int
			}{
				ConfigID:  configID,
				ChannelID: channelID,
			})
		}
	}

	// 4. 创建新的关联关系
	var existingRelations []*dto.ConfigChannelRelationDto
	var newRelations []*dto.ConfigChannelRelationDto

	for _, combo := range combinations {
		// 创建配置渠道关联
		err := c.repo.CreateConfigChannelRelation(combo.ConfigID, combo.ChannelID)
		if err != nil {
			c.log.Error("创建配置渠道关联失败: %v", err)
			continue
		}

		// 查询创建的关联
		queryDto := &dto.ConfigChannelRelationDto{
			MarketingChannelID: combo.ChannelID,
		}
		relations, err := c.repo.List(queryDto)
		if err != nil {
			c.log.Error("查询配置渠道关联失败: %v", err)
			continue
		}

		for _, relation := range relations {
			if relation.ConfigID == combo.ConfigID {
				newRelations = append(newRelations, relation)
				break
			}
		}
	}

	// 转换为外部响应
	responseList := c.convertToResponseList(newRelations)

	c.log.Info("批量创建配置渠道关联完成，总数: %d，新建: %d，已存在: %d",
		len(newRelations)+len(existingRelations), len(newRelations), len(existingRelations))

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data": gin.H{
			"total":       len(newRelations) + len(existingRelations),
			"new_created": len(newRelations),
			"existed":     len(existingRelations),
			"relations":   responseList,
		},
	})
}

// ConfigChannelRelationDelete 删除配置渠道关联
func (c *ConfigChannelRelationController) ConfigChannelRelationDelete(ctx *gin.Context) {
	type ConfigChannelRelationDeleteRequest struct {
		ID int `json:"id" binding:"required,min=1"` // 主键ID
	}
	var req ConfigChannelRelationDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 删除配置渠道关联
	err := c.repo.DeleteConfigChannelRelation(req.ID)
	if err != nil {
		c.log.Error("删除配置渠道关联失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "删除失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// ConfigChannelRelationBatchDelete 批量删除配置渠道关联
func (c *ConfigChannelRelationController) ConfigChannelRelationBatchDelete(ctx *gin.Context) {
	type ConfigChannelRelationBatchDeleteRequest struct {
		Ids []int `json:"ids" binding:"required"` // 主键ID列表，逗号分隔
	}
	var req ConfigChannelRelationBatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 解析ID列表
	ids := req.Ids

	// 批量删除配置渠道关联
	var successCount int
	var failedCount int

	for _, id := range ids {

		err := c.repo.DeleteConfigChannelRelation(id)
		if err != nil {
			failedCount++
		} else {
			successCount++
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "批量删除成功",
		"data": gin.H{
			"total":   len(ids),
			"success": successCount,
			"failed":  failedCount,
		},
	})
}
