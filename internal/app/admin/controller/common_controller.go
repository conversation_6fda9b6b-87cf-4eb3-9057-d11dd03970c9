package controller

import (
	"chongli/component"
	"chongli/component/apollo"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/pkg/logger"
	"net/http"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
)

// CommonController 公共控制器
type CommonController struct {
	log           *logger.Logger
	uploadService *service.UploadService
	config        *apollo.Config
}

// UploadVideo 上传媒体文件（音频/视频）
func (c *CommonController) UploadVideo(ctx *gin.Context) {
	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		c.log.Error("获取上传文件失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请选择要上传的文件",
		})
		return
	}

	// 验证文件格式 - 只允许音频和视频格式
	if !c.isValidMediaFormat(file.Filename) {
		c.log.Error("媒体文件格式不支持: %s", file.Filename)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "只支持上传音频(mp3, wav, aac, flac, m4a, ogg, wma)或视频(mp4, avi, mov, mkv, flv, wmv)格式",
		})
		return
	}

	// 构造上传请求DTO
	req := &dto.UploadRequestDto{
		File: file,
	}

	// 调用上传服务
	url, err := c.uploadService.Upload(req)
	if err != nil {
		c.log.Error("上传文件失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	// 获取静态文件域名配置
	domain := c.config.QiniuyunDomain

	// 返回上传结果
	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "上传成功",
		"data": gin.H{
			"url":    url,
			"domain": domain,
		},
	})
}

// NewCommonController 创建公共控制器实例
func NewCommonController(bootStrap *component.BootStrap) *CommonController {
	return &CommonController{
		log:           bootStrap.Log,
		uploadService: service.NewUploadService(),
		config:        bootStrap.Config,
	}
}

// UploadImg 上传图片
func (c *CommonController) UploadImg(ctx *gin.Context) {
	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		c.log.Error("获取上传文件失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请选择要上传的文件",
		})
		return
	}

	// 验证文件格式 - 只允许图片格式
	if !c.isValidImageFormat(file.Filename) {
		c.log.Error("图片格式不支持: %s", file.Filename)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "只支持上传图片格式文件(jpg, jpeg, png, gif, webp)",
		})
		return
	}

	// 构造上传请求DTO
	req := &dto.UploadRequestDto{
		File: file,
	}

	// 调用上传服务
	url, err := c.uploadService.Upload(req)
	if err != nil {
		c.log.Error("上传文件失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	// 获取静态文件域名配置
	domain := c.config.QiniuyunDomain

	// 返回上传结果
	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "上传成功",
		"data": gin.H{
			"url":    url,
			"domain": domain,
		},
	})
}

// UploadFile 上传文件（通用）
func (c *CommonController) UploadFile(ctx *gin.Context) {
	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		c.log.Error("获取上传文件失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请选择要上传的文件",
		})
		return
	}

	// 验证文件格式 - 只允许视频和压缩包格式
	if !c.isValidFileFormat(file.Filename) {
		c.log.Error("文件格式不支持: %s", file.Filename)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "只支持上传视频格式(mp4, avi, mov, mkv, flv, wmv)或压缩包格式(zip, rar, 7z, tar, gz)",
		})
		return
	}

	// 构造上传请求DTO
	req := &dto.UploadRequestDto{
		File: file,
	}

	// 调用上传服务
	url, err := c.uploadService.Upload(req)
	if err != nil {
		c.log.Error("上传文件失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	// 获取静态文件域名配置
	domain := c.config.QiniuyunDomain

	// 返回上传结果
	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "上传成功",
		"data": gin.H{
			"url":    url,
			"domain": domain,
		},
	})
}

// isValidImageFormat 验证图片格式
func (c *CommonController) isValidImageFormat(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	allowedImageExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}
	for _, allowedExt := range allowedImageExts {
		if ext == allowedExt {
			return true
		}
	}
	return false
}

// isValidFileFormat 验证文件格式（视频和压缩包）
func (c *CommonController) isValidFileFormat(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	// 视频格式
	allowedVideoExts := []string{".mp4", ".avi", ".mov", ".mkv", ".flv", ".wmv"}
	// 压缩包格式
	allowedArchiveExts := []string{".zip", ".rar", ".7z", ".tar", ".gz", ".json"}

	// 合并所有允许的格式
	allowedExts := append(allowedVideoExts, allowedArchiveExts...)

	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			return true
		}
	}
	return false
}

// isValidMediaFormat 验证媒体文件格式（音频或视频）
func (c *CommonController) isValidMediaFormat(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	// 视频格式
	allowedVideoExts := []string{".mp4", ".avi", ".mov", ".mkv", ".flv", ".wmv"}
	// 音频格式
	allowedAudioExts := []string{".mp3", ".wav", ".aac", ".flac", ".m4a", ".ogg", ".wma"}

	for _, allowedExt := range allowedVideoExts {
		if ext == allowedExt {
			return true
		}
	}
	for _, allowedExt := range allowedAudioExts {
		if ext == allowedExt {
			return true
		}
	}
	return false
}
