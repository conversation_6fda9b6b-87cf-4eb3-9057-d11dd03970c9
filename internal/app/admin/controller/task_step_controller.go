package controller

import (
	"chongli/component"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type TaskStepController struct {
	log          *logger.Logger
	taskStepRepo repo.TaskStepRepo
}

func NewTaskStepController(
	bootStrap *component.BootStrap,
	taskStepRepo repo.TaskStepRepo,
) *TaskStepController {
	return &TaskStepController{
		log:          bootStrap.Log,
		taskStepRepo: taskStepRepo,
	}
}

// TaskStepListRequest 任务步骤列表请求
type TaskStepListRequest struct {
	Page     int    `form:"page" binding:"required,min=1"`
	PageSize int    `form:"page_size"`
	TaskID   uint64 `form:"task_id"`
}

// TaskStepListResponse 任务步骤列表响应
type TaskStepListResponse struct {
	ID          int64  `json:"id"`
	TaskID      int64  `json:"task_id"`
	Step        int32  `json:"step"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Action      string `json:"action"`
	Params      string `json:"params"`
	Sort        int32  `json:"sort"`
	Status      int32  `json:"status"`
	CreateTime  string `json:"create_time"`
	UpdateTime  string `json:"update_time"`
}

// UpdateTaskStepRequest 更新任务步骤请求
type UpdateTaskStepRequest struct {
	ID          int64  `json:"id" binding:"required"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Action      string `json:"action"`
	Params      string `json:"params"`
	Sort        int32  `json:"sort"`
	Status      int32  `json:"status"`
}

// TaskStepList 获取任务步骤列表
func (c *TaskStepController) TaskStepList(ctx *gin.Context) {
	var req TaskStepListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	query := &dto.GetStepQuery{}

	// 构造查询条件
	stepQuery := &dto.TaskStepDTO{}
	if req.TaskID > 0 {
		stepQuery.WorkID = req.TaskID
	}
	query.Step = stepQuery

	list, err := c.taskStepRepo.GetStepQuery(ctx, query)
	if err != nil {
		c.log.Error("获取任务步骤列表失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError("获取任务步骤列表失败"), response.WithSLSLog)
		return
	}

	//  repo 未提供Count方法，暂时返回0
	total, err := c.taskStepRepo.Count(ctx, query)
	if err != nil {
		c.log.Error("获取任务步骤总数失败: %v", err)
		total = 0
	}

	var output []*TaskStepListResponse
	for _, item := range list {
		output = append(output, &TaskStepListResponse{
			ID:          item.ID,
			TaskID:      int64(item.WorkID),
			Step:        int32(item.StepIndex),
			Name:        item.StepName,
			Description: "", // DTO中没有描述字段
			Action:      "", // DTO中没有Action字段
			Params:      "", // DTO中Params是JSONMap类型，暂时留空
			Sort:        0,  // DTO中没有Sort字段
			Status:      0,  // DTO中Status是string类型，暂时留空
			CreateTime:  item.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdateTime:  item.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	responseData := map[string]interface{}{
		"list":      output,
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
	}

	response.Response(ctx, nil, responseData, nil, response.WithSLSLog)
}

// UpdateTaskStep 更新任务步骤
func (c *TaskStepController) UpdateTaskStep(ctx *gin.Context) {
	var req UpdateTaskStepRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	updates := make(map[string]any)
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Action != "" {
		updates["action"] = req.Action
	}
	if req.Params != "" {
		updates["params"] = req.Params
	}
	if req.Sort > 0 {
		updates["sort"] = req.Sort
	}
	if req.Status >= 0 {
		updates["status"] = req.Status
	}

	if len(updates) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewLowError("没有需要更新的字段"), response.WithSLSLog)
		return
	}

	if err := c.taskStepRepo.Update(ctx, req.ID, updates); err != nil {
		c.log.Error("更新任务步骤失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError("更新失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, "更新成功", nil, response.WithSLSLog)
}

// DeleteTaskStep 删除任务步骤
func (c *TaskStepController) DeleteTaskStep(ctx *gin.Context) {
	idStr := ctx.Query("id")
	if idStr == "" {
		response.Response(ctx, nil, nil, errpkg.NewLowError("缺少id参数"), response.WithSLSLog)
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("id参数格式错误"), response.WithSLSLog)
		return
	}

	if err := c.taskStepRepo.Delete(ctx, id); err != nil {
		c.log.Error("删除任务步骤失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError("删除失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, "删除成功", nil, response.WithSLSLog)
}
