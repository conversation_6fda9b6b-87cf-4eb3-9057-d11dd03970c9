package registry

import (
	"chongli/component"
	"chongli/internal/app/task/processors"

	"github.com/robfig/cron/v3"
)

type PicTask struct {
	bootstrap *component.BootStrap
	pic       *processors.PicTaskProcessors
	dance     *processors.DanceTaskProcessors
	avatar    *processors.AvatarPictureProcessors
}

func NewPicTask(
	c *cron.Cron,
	bootstrap *component.BootStrap,
	p *processors.PicTaskProcessors,
	d *processors.DanceTaskProcessors,
	a *processors.AvatarPictureProcessors,
) *PicTask {
	picTask := &PicTask{
		bootstrap: bootstrap,
		pic:       p,
		dance:     d,
		avatar:    a,
	}

	// 写真
	_, _ = c.AddFunc("@every 11s", picTask.pic.AiCompound)
	_, _ = c.AddFunc("@every 12s", picTask.pic.AddChangeStyle)
	_, _ = c.AddFunc("@every 13s", picTask.pic.GetStyleResultAndChangeFace)

	// 跳舞
	_, _ = c.AddFunc("@every 15s", picTask.dance.PushTask)
	_, _ = c.AddFunc("@every 16s", picTask.dance.QueryResult)

	// 对口型相关的任务 - 火山引擎API
	//_, _ = c.AddFunc("@every 17s", picTask.avatar.PushRoleTask)
	//_, _ = c.AddFunc("@every 18s", picTask.avatar.GetRoleTask)
	//_, _ = c.AddFunc("@every 19s", picTask.avatar.GetAvatarPictureTask)

	// 唱歌
	// 对口型任务 - 腾讯混元API
	// 提交对口型任务
	_, _ = c.AddFunc("@every 17s", picTask.avatar.SubmitTencentPortraitSingJob)
	// 获取任务结果
	_, _ = c.AddFunc("@every 18s", picTask.avatar.GetTencentPortraitSingResult)
	return picTask
}
