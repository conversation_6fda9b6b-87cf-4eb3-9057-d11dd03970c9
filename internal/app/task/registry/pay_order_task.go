package registry

import (
	"chongli/component"
	"chongli/internal/app/task/processors"

	"github.com/robfig/cron/v3"
)

// PayOrderTask 支付订单定时任务管理器
type PayOrderTask struct {
	bootstrap           *component.BootStrap
	payOrderTaskService *processors.PayOrderTaskProcessors
}

func NewPayOrderTask(c *cron.Cron, bootstrap *component.BootStrap, payOrderTaskService *processors.PayOrderTaskProcessors) *PayOrderTask {
	payOrderTask := &PayOrderTask{
		bootstrap:           bootstrap,
		payOrderTaskService: payOrderTaskService,
	}

	// 注册定时任务
	// 每天凌晨四点清理过期订单定时任务
	_, _ = c.AddFunc("0 0 4 * * *", payOrderTask.payOrderTaskService.CleanExpiredOrders)

	// 测试用，每10秒执行一次
	// _, _ = c.AddFunc("@every 10s", payOrderTask.payOrderTaskService.CleanExpiredOrders)

	return payOrderTask
}
