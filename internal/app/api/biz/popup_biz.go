package biz

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"chongli/pkg/utils"
	"fmt"

	"github.com/gin-gonic/gin"
)

type PopupBiz struct {
	log              *logger.Logger
	guiyinApiService *service.GuiyinService
	channelPopupRepo repo.ChannelPopupRepo
}

type PopupListRequest struct {
	utils.RequestHeaderDto
}

type PopupListResponse struct {
	dto.PopupDto
}

func NewPopupBiz(bootStrap *component.BootStrap, guiyinApiService *service.GuiyinService, channelPopupRepo repo.ChannelPopupRepo) *PopupBiz {
	return &PopupBiz{
		log:              bootStrap.Log,
		guiyinApiService: guiyinApiService,
		channelPopupRepo: channelPopupRepo,
	}
}

func (p *PopupBiz) GetPopupList(_ *gin.Context, req *PopupListRequest) ([]*PopupListResponse, error) {
	fmt.Println("req: ", req)
	fmt.Println("p.guiyinApiService: ", p.guiyinApiService)

	channelId, err := p.guiyinApiService.GetChannelIDByDeviceID(req.DeviceId, req.Channel)
	if err != nil {
		p.log.Error("获取渠道ID失败: %v", err)
		return nil, err
	}
	no := int8(model.StatusDisabled)
	popupList, err := p.channelPopupRepo.GetChannelPopupWithDetails(&dto.ChannelPopupQueryDto{
		ChannelID:        channelId,
		ChannelIsDeleted: &no,
		PopupIsDeleted:   &no,
	})
	if err != nil {
		p.log.Error("获取弹窗列表失败: %v", err)
		return nil, err
	}
	if len(popupList) == 0 {
		return nil, nil
	}
	returnList := make([]*PopupListResponse, 0)
	for _, popup := range popupList {
		returnList = append(returnList, &PopupListResponse{
			PopupDto: *popup.Popup,
		})
	}
	return returnList, nil
}
