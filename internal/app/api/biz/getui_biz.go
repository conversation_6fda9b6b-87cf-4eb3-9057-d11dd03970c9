package biz

import (
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/response"
)

type GeTuiBiz struct {
	userBindGetuiRepo repo.UserBindGetuiRepo
}

func NewGeTuiBiz(userBindGetuiRepo repo.UserBindGetuiRepo) *GeTuiBiz {
	return &GeTuiBiz{
		userBindGetuiRepo: userBindGetuiRepo,
	}
}

func (b *GeTuiBiz) BindGeTui(req *dto.BindGeTuiCidRequest) (string, errpkg.IError) {
	// 先判断是否已经有绑定的cid
	cid, err := b.userBindGetuiRepo.GetBindInfoByUserId(req.UserId)
	if err != nil {
		return "", errpkg.NewHighError(response.DbError)
	}
	if cid.Id != 0 {
		// 更新绑定
		if err := b.userBindGetuiRepo.UpdateBind(req.UserId, req.ClientID); err != nil {
			return "", errpkg.NewHighError(response.DbError)
		}
		return "OK", nil
	}
	// 绑定
	if err := b.userBindGetuiRepo.BindUserIdAndCid(req.UserId, req.ClientID); err != nil {
		return "", errpkg.NewHighError(response.DbError)
	}
	return "OK", nil
}
