package biz

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
)

type ConfigBiz struct {
	log                       *logger.Logger
	configRepo                repo.ConfigRepo
	guiyinService             *service.GuiyinService
	configChannelRelationRepo repo.ConfigChannelRelationRepo
	versionRepo               repo.VersionRepo
}

func NewConfigBiz(bootStrap *component.BootStrap,
	configRepo repo.ConfigRepo,
	guiyinService *service.GuiyinService,
	configChannelRelationRepo repo.ConfigChannelRelationRepo,
	versionRepo repo.VersionRepo,
) *ConfigBiz {
	return &ConfigBiz{
		log:                       bootStrap.Log,
		configRepo:                configRepo,
		guiyinService:             guiyinService,
		configChannelRelationRepo: configChannelRelationRepo,
		versionRepo:               versionRepo,
	}
}

// ConfigListRequest API配置列表请求
type ConfigListRequest struct {
	*utils.RequestHeaderDto
	ConfigKey string `json:"config_key" form:"config_key"` // 配置名
}

// ConfigItem 配置项
type ConfigItem struct {
	ConfigKey   string `json:"config_key"`   // 配置名
	ConfigValue string `json:"config_value"` // 配置值
	Remark      string `json:"remark"`       // 备注信息
}

type VersionResponse struct {
	Channel     string `json:"channel"`
	Version     string `json:"version"`
	Desc        string `json:"desc"`
	DownloadUrl string `json:"download_url"`
	IsForce     int8   `json:"is_force"`
}

// ConfigList 获取已发布的配置列表
func (c *ConfigBiz) ConfigList(req *ConfigListRequest) (map[string]any, errpkg.IError) {

	// 构建查询条件：只查询已发布且未删除的配置
	queryDto := &dto.ConfigQueryDto{
		ConfigKey: req.ConfigKey,
		IsDelete:  model.StatusDisabled, // 未删除
		IsPublish: model.StatusEnabled,  // 已发布
		IsChannel: model.StatusDisabled, // 非渠道
	}

	// 获取配置列表
	configs, err := c.configRepo.GetConfigList(queryDto)
	if err != nil {
		c.log.Error("获取配置列表失败: %v", err)
		return nil, errpkg.NewHighError(response.DbError)
	}
	defaultConfigs := map[string]*dto.ConfigDto{}

	for _, config := range configs {
		defaultConfigs[config.ConfigKey] = config
	}
	resp := map[string]any{}
	// 先将所有默认配置加入到结果map中
	for key, config := range defaultConfigs {
		resp[key] = config.ConfigValue
		resp[key+"_remark"] = config.Remark
	}
	channelConfig, err := c.getChannelConfig(req)
	if err != nil {
		return nil, errpkg.NewHighError(response.DbError)
	}

	//使用渠道配置覆盖默认配置
	for key, config := range channelConfig {
		resp[key] = config.ConfigValue
		resp[key+"_remark"] = config.Remark
	}
	return resp, nil
}

func (c *ConfigBiz) getChannelConfig(req *ConfigListRequest) (map[string]*dto.ConfigDto, error) {

	channelId, err := c.guiyinService.GetChannelIDByDeviceID(req.DeviceId, req.Channel)
	if err != nil {
		return nil, err
	}
	if channelId == 0 {
		return nil, nil
	}

	// 创建查询DTO
	queryDto := &dto.ConfigChannelRelationDto{
		MarketingChannelID: channelId,
		PageSize:           999,
	}

	// 使用List方法查询
	configList, err := c.configChannelRelationRepo.List(queryDto)
	if err != nil {
		c.log.Error("获取配置列表失败: %v", err)
		return nil, errpkg.NewHighError(response.DbError)
	}
	returnMap := map[string]*dto.ConfigDto{}
	for _, config := range configList {
		returnMap[config.Config.ConfigKey] = config.Config
	}
	return returnMap, nil

}

func (c *ConfigBiz) GetVersion(os string) (*VersionResponse, error) {
	version, err := c.versionRepo.SelectLatestVersion(os)
	if err != nil {
		return nil, errpkg.NewHighError(response.DbError)
	}

	if version.Version == "" || version.VersionInt == 0 {
		return nil, nil
	}

	versionResponse := &VersionResponse{
		Channel:     version.Channel,
		Version:     version.Version,
		Desc:        version.Desc,
		DownloadUrl: version.DownloadUrl,
		IsForce:     int8(version.IsForce),
	}
	return versionResponse, nil
}
