package controller

import (
	"chongli/component"
	"chongli/internal/app/api/biz"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"

	"github.com/gin-gonic/gin"
)

type UserGiveController struct {
	userGiveBiz *biz.UserGiveBiz
	log         *logger.Logger
}

func NewUserGiveController(
	bootstrap *component.BootStrap,
	userGiveBiz *biz.UserGiveBiz,
) *UserGiveController {
	return &UserGiveController{
		log:         bootstrap.Log,
		userGiveBiz: userGiveBiz,
	}
}

// GiveEveryDayFreeDiamond 赠送每日免费钻石
func (c *UserGiveController) GiveEveryDayFreeDiamond(ctx *gin.Context) {
	req := make(map[string]any)

	userId := utils.GetUserId(ctx)
	if userId < 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(response.UserIdError), response.WithSLSLog)
		return
	}

	req["userId"] = userId
	req["version"] = ctx.GetHeader("version")
	req["channel"] = ctx.GetHeader("channel")

	// 调用业务层赠送钻石
	err := c.userGiveBiz.GiveUserEveryDayFreeDiamond(ctx.Request.Context(), req)
	if err != nil {
		response.Response(ctx, nil, nil, err, response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, "赠送成功", nil, response.WithSLSLog)
}
