package controller

import (
	"chongli/component"
	"chongli/component/apollo"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/pkg/logger"
	"crypto/md5"
	"fmt"
	"github.com/gin-gonic/gin"
	"sort"
	"strings"
)

type PayNotifyController struct {
	log              *logger.Logger
	payNotifyService *service.PayNotifyService
}

func NewPayNotifyController(
	bootStrap *component.BootStrap,
	payNotifyBiz *service.PayNotifyService,
) *PayNotifyController {
	return &PayNotifyController{
		log:              bootStrap.Log,
		payNotifyService: payNotifyBiz,
	}
}

func (c *PayNotifyController) PayNotify(ctx *gin.Context) {
	var req dto.PayNotifyDto
	if err := ctx.ShouldBindQuery(&req); err != nil {
		c.log.Error("支付回调绑定参数错误: %v", err)
		return
	}

	if apollo.GetApolloConfig().Env == "prod" {
		c.log.Info("生产环境 进行验签 参数: %v", req)
		if !c.checkToken(&req) {
			c.log.Error("支付回调参数验签失败")
			return
		}
	} else {
		c.log.Info("非生产环境 不进行验签 参数: %v", req)
	}

	_ = c.payNotifyService.PayNotify(&req)
}

// 验签 将回调携带的参数加上 partnerIdKey 和 partnerIdPwd 按照字典序排序拼接 如 key1=val1&... 进行MD5加密
func (c *PayNotifyController) checkToken(req *dto.PayNotifyDto) bool {
	params := make(map[string]string)
	// 必须参数
	params["orderId"] = req.WnlOrderId
	params["mhtOrderNo"] = req.OrderId
	params["goodsID"] = req.GoodsMiddleId
	params["payType"] = fmt.Sprintf("%d", req.PayType)
	params["fee"] = req.Fee
	params["extraData"] = req.ExtraData
	params["parterIdKey"] = apollo.GetApolloConfig().PartnerIdKey
	params["parterIdPwd"] = apollo.GetApolloConfig().PartnerIdPwd
	// 非必需参数
	if req.NotifyType != nil {
		params["notifyType"] = *req.NotifyType
	}
	if req.Key != nil {
		params["key"] = fmt.Sprintf("%d", *req.Key)
	}
	// 按照字典序排序
	var keys []string
	for key := range params {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	// 拼接字符串
	var sb strings.Builder
	for _, key := range keys {
		sb.WriteString(fmt.Sprintf("%s=%s&", key, params[key]))
	}
	// 去掉最后一个多余的 &
	paramString := strings.TrimSuffix(sb.String(), "&")
	// 进行MD5加密
	sign := fmt.Sprintf("%x", md5.Sum([]byte(paramString)))
	return req.Token == strings.ToUpper(sign)
}
