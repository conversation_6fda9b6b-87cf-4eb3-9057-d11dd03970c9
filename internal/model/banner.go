package model

import "time"

type Banner struct {
	ID            int64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                                      // 主键ID
	Title         string    `gorm:"column:title;NOT NULL" json:"title"`                                                  // 标题
	Image         string    `gorm:"column:image;NOT NULL" json:"image"`                                                  // 图片URL
	Video         string    `gorm:"column:video;NOT NULL" json:"video"`                                                  // 视频URL
	Desc          string    `gorm:"column:desc;NOT NULL" json:"desc"`                                                    // 描述
	Sort          int       `gorm:"column:sort;default:0;NOT NULL" json:"sort"`                                          // 排序，数字越小越靠前
	IsActive      int8      `gorm:"column:is_active;default:1;NOT NULL" json:"is_active"`                                // 是否启用：1-启用；-1-禁用
	CreateAt      time.Time `gorm:"column:create_at;default:CURRENT_TIMESTAMP;NOT NULL;autoCreateTime" json:"create_at"` // 创建时间
	UpdateAt      time.Time `gorm:"column:update_at;default:CURRENT_TIMESTAMP;NOT NULL;autoUpdateTime" json:"update_at"` // 更新时间
	IsDelete      int8      `gorm:"column:is_delete;default:-1;NOT NULL" json:"is_delete"`                               // 是否删除：-1-未删除；1-已删除
	MaxVersion    string    `gorm:"column:max_version;NOT NULL" json:"max_version"`                                      // 最大版本号
	MaxVersionInt int64     `gorm:"column:max_version_int;NOT NULL" json:"max_version_int"`                              // 最大版本号整数值
	TemplateId    int64     `gorm:"column:template_id;default:0;NOT NULL" json:"template_id"`                            // 跳转模板ID
	CategoryId    int64     `gorm:"column:category_id;default:0;NOT NULL" json:"category_id"`                            // 跳转分类ID
	CategoryName  string    `gorm:"column:category_name;default:'';NOT NULL" json:"category_name"`                       // 跳转分类名称
	Location      string    `gorm:"column:location;default:'index';NOT NULL" json:"location"`                            // 位置标识，区分不同位置的Banner
}

func (b *Banner) TableName() string {
	return "banner"
}
