package model

import (
	"time"
)

// AITemplate AI 内容生成模板
type AITemplate struct {
	ID               uint64            `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	Name             string            `gorm:"column:name;NOT NULL" json:"name"`
	CoverURL         string            `gorm:"column:cover_url;type:varchar(255)" json:"cover_url"`
	VideoCoverURL    string            `gorm:"column:video_cover_url;type:varchar(255)" json:"video_cover_url"`
	Status           int8              `gorm:"column:status;default:1;NOT NULL" json:"status"`
	MaxVersion       string            `gorm:"column:max_version;NOT NULL" json:"max_version"`         // 最大适用版本
	MaxVersionInt    int64             `gorm:"column:max_version_int;NOT NULL" json:"max_version_int"` // 最大适用版本int值
	SortOrder        int               `gorm:"column:sort_order;default:0;NOT NULL" json:"sort_order"`
	CategoryID       uint64            `gorm:"column:category_id;type:varchar(50)" json:"category_id"`
	VariablesJSON    string            `gorm:"column:variables_json;type:json" json:"variables_json"`
	Description      string            `gorm:"column:description;type:text" json:"description"`
	CreateAt         time.Time         `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_at"`
	UpdatedAt        time.Time         `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL;autoUpdateTime" json:"update_at"`
	TemplateCategory *TemplateCategory `gorm:"foreignKey:CategoryID;references:ID" json:"template_category"`
	DiamondCost      int               `gorm:"column:diamond_cost;default:0;NOT NULL" json:"diamond_cost"` // 钻石花费
	MainClass        int               `gorm:"column:main_class;default:0;NOT NULL" json:"main_class"`     // 主分类 1-写真 2-唱歌 3-跳舞
}

// TableName 表名称
func (ai *AITemplate) TableName() string {
	return "template_ai"
}
