package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// Task 异步任务主表
// type Task struct {
// 	ID          int64     `json:"id" gorm:"column:id;primaryKey;autoIncrement"`       // 任务 ID
// 	TaskType    string    `json:"task_type" gorm:"column:task_type;not null"`         // 任务类型（如 multi_step / single_step / email / export）
// 	Status      string    `json:"status" gorm:"column:status;default:pending"`        // 任务整体状态（pending / running / done / failed）
// 	CurrentStep int       `json:"current_step" gorm:"column:current_step;default:0"`  // 当前执行到第几步（用于简化查询）
// 	RetryCount  int       `json:"retry_count" gorm:"column:retry_count;default:0"`    // 任务重试次数
// 	ErrorMsg    string    `json:"error_msg" gorm:"column:error_msg;type:text"`        // 任务级别的错误信息
// 	CreatedAt   time.Time `json:"created_at" gorm:"column:created_at;autoCreateTime"` // 创建时间
// 	UpdatedAt   time.Time `json:"updated_at" gorm:"column:updated_at;autoUpdateTime"` // 更新时间
// 	UserID      int64     `json:"user_id" gorm:"column:user_id;not null"`             // 用户ID
// 	DeviceID    string    `json:"device_id" gorm:"column:device_id;not null"`         // 设备ID
// 	WorkID      uint64    `json:"work_id" gorm:"column:work_id;not null"`             // 用户的作品id
// }

// TableName 表名称
// func (*Task) TableName() string {
// 	return "tasks"
// }

// TaskStep 异步任务步骤表
type TaskStep struct {
	ID int64 `json:"id" gorm:"column:id;primaryKey;autoIncrement"` // 步骤记录ID
	// TaskID     int64      `json:"task_id" gorm:"column:task_id;not null"`             // 所属任务ID
	StepIndex  int        `json:"step_index" gorm:"column:step_index;not null"`       // 步骤序号，从0开始
	StepName   string     `json:"step_name" gorm:"column:step_name;not null"`         // 步骤名称，例如 download、convert、upload
	Status     string     `json:"status" gorm:"column:status;default:pending"`        // 步骤状态（pending / running / done / failed）
	RetryCount int        `json:"retry_count" gorm:"column:retry_count;default:0"`    // 本步骤的重试次数
	Params     string     `json:"params" gorm:"column:params;type:json"`              // 该步骤的自定义参数（JSON）
	Result     string     `json:"result" gorm:"column:result;type:json"`              // 步骤执行结果，可选记录输出数据
	ErrorMsg   string     `json:"error_msg" gorm:"column:error_msg;type:text"`        // 错误信息
	StartedAt  *time.Time `json:"started_at" gorm:"column:started_at"`                // 开始执行时间
	FinishedAt *time.Time `json:"finished_at" gorm:"column:finished_at"`              // 完成时间
	CreatedAt  time.Time  `json:"created_at" gorm:"column:created_at;autoCreateTime"` // 创建时间
	UpdatedAt  time.Time  `json:"updated_at" gorm:"column:updated_at;autoUpdateTime"` // 更新时间
	// Task       Task       `json:"task" gorm:"foreignKey:TaskID;references:ID"`        // 所属任务
	WorkID   uint64    `json:"work_id" gorm:"column:work_id;not null"`           // 用户的作品id
	UserWork UserWorks `json:"user_work" gorm:"foreignKey:WorkID;references:ID"` // 所属作品
}

// TableName 表名称
func (*TaskStep) TableName() string {
	return "task_steps"
}

// 步骤状态常量
const (
	StepStatusInit          = "init"           // 待初始化
	StepStatusPending       = "pending"        // 待执行
	StepStatusRunning       = "running"        // 执行中
	StepStatusWaitingResult = "waiting_result" // 等待结果
	StepStatusDone          = "done"           // 已完成
	StepStatusFailed        = "failed"         // 执行失败
)

// JSONMap 用于处理JSON类型字段
type JSONMap map[string]any

// Value 实现 driver.Valuer 接口
func (j JSONMap) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现 sql.Scanner 接口
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言为[]byte失败")
	}

	if len(bytes) == 0 {
		*j = nil
		return nil
	}

	return json.Unmarshal(bytes, j)
}

// MarshalJSON 自定义JSON序列化
func (j JSONMap) MarshalJSON() ([]byte, error) {
	if j == nil {
		return []byte("null"), nil
	}
	return json.Marshal(map[string]interface{}(j))
}

// UnmarshalJSON 自定义JSON反序列化
func (j *JSONMap) UnmarshalJSON(data []byte) error {
	if j == nil {
		return errors.New("JSONMap: UnmarshalJSON on nil pointer")
	}
	return json.Unmarshal(data, (*map[string]interface{})(j))
}
