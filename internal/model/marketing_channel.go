package model

import "time"

// MarketingChannel 营销渠道配置
type MarketingChannel struct {
	ID        int       `json:"id" gorm:"column:id;primaryKey;autoIncrement"`     // 主键id
	Type      int8      `json:"type" gorm:"column:type;not null"`                 // 类型：1-匹配商店渠道，2-匹配归因推广
	Title     string    `json:"title" gorm:"column:title"`                        // 标题
	BindKey   string    `json:"bind_key" gorm:"column:bind_key;not null"`         // 绑定键
	BindValue string    `json:"bind_value" gorm:"column:bind_value;not null"`     // 绑定值
	IsDeleted int8      `json:"is_deleted" gorm:"column:is_deleted;default:-1"`   // 是否删除：-1-未删除，1-已删除
	CreateAt  time.Time `json:"create_at" gorm:"column:create_at;autoCreateTime"` // 创建时间
	UpdateAt  time.Time `json:"update_at" gorm:"column:update_at;autoUpdateTime"` // 更新时间
}

// TableName 表名称
func (*MarketingChannel) TableName() string {
	return "channel"
}

// 渠道类型常量
const (
	ChannelTypeStore       = 1 // 匹配商店渠道
	ChannelTypeAttribution = 2 // 匹配归因推广
)
