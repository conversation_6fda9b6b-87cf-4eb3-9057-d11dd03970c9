package model

import (
	"time"
)

// AdminUser 后台管理用户
type AdminUser struct {
	ID            int64      `json:"id" gorm:"column:id;primaryKey;autoIncrement"`    // 主键id
	Email         string     `json:"email" gorm:"column:email;uniqueIndex;not null"`  // 邮箱地址（登录凭证）
	Nickname      string     `json:"nickname" gorm:"column:nickname;not null"`        // 用户昵称
	Avatar        string     `json:"avatar" gorm:"column:avatar"`                     // 头像地址
	LastLoginTime *time.Time `json:"last_login_time" gorm:"column:last_login_time"`   // 最后登录时间
	LastLoginIP   string     `json:"last_login_ip" gorm:"column:last_login_ip"`       // 最后登录IP
	LoginCount    int64      `json:"login_count" gorm:"column:login_count;default:0"` // 登录次数

	CreatedBy int64      `json:"created_by" gorm:"column:created_by"`              // 创建者ID
	UpdatedBy int64      `json:"updated_by" gorm:"column:updated_by"`              // 更新者ID
	CreateAt  time.Time  `json:"create_at" gorm:"column:create_at;autoCreateTime"` // 创建时间
	UpdateAt  time.Time  `json:"update_at" gorm:"column:update_at;autoUpdateTime"` // 更新时间
	IsDelete  StatusFlag `json:"is_delete" gorm:"column:is_delete;default:-1"`     // 是否已被删除，-1：未删除；1：已删除
}

// TableName 表名称
func (*AdminUser) TableName() string {
	return "admin_user"
}

// EmailVerifyCode 邮箱验证码
type EmailVerifyCode struct {
	ID        int64      `json:"id" gorm:"column:id;primaryKey;autoIncrement"`     // 主键id
	Email     string     `json:"email" gorm:"column:email;index;not null"`         // 邮箱地址
	Code      string     `json:"code" gorm:"column:code;not null"`                 // 验证码
	Purpose   int8       `json:"purpose" gorm:"column:purpose;not null"`           // 用途：1-登录，2-重置密码，3-其他
	ExpiredAt time.Time  `json:"expired_at" gorm:"column:expired_at;not null"`     // 过期时间
	IsUsed    StatusFlag `json:"is_used" gorm:"column:is_used;default:-1"`         // 是否已使用：-1-未使用，1-已使用
	ClientIP  string     `json:"client_ip" gorm:"column:client_ip"`                // 客户端IP
	CreateAt  time.Time  `json:"create_at" gorm:"column:create_at;autoCreateTime"` // 创建时间
	UpdateAt  time.Time  `json:"update_at" gorm:"column:update_at;autoUpdateTime"` // 更新时间
}

// TableName 表名称
func (*EmailVerifyCode) TableName() string {
	return "email_verify_code"
}

// AdminLoginLog 后台登录日志
type AdminLoginLog struct {
	ID        int64      `json:"id" gorm:"column:id;primaryKey;autoIncrement"`     // 主键id
	UserID    int64      `json:"user_id" gorm:"column:user_id;index;not null"`     // 用户ID
	Email     string     `json:"email" gorm:"column:email;not null"`               // 登录邮箱
	LoginIP   string     `json:"login_ip" gorm:"column:login_ip"`                  // 登录IP
	UserAgent string     `json:"user_agent" gorm:"column:user_agent"`              // 用户代理
	Status    StatusFlag `json:"status" gorm:"column:status;not null"`             // 登录状态：-1-失败，1-成功
	Message   string     `json:"message" gorm:"column:message"`                    // 登录消息
	CreateAt  time.Time  `json:"create_at" gorm:"column:create_at;autoCreateTime"` // 创建时间
}

// TableName 表名称
func (*AdminLoginLog) TableName() string {
	return "admin_login_log"
}
