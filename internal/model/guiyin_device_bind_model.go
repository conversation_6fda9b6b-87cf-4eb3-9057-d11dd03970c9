package model

import (
	"time"
)

// GuiyinDeviceBind 设备归因绑定模型
type GuiyinDeviceBind struct {
	Id        int              `json:"id" gorm:"column:id;primaryKey;autoIncrement"`     // 主键ID
	DeviceId  string           `json:"device_id" gorm:"column:device_id;uniqueIndex"`    // 设备ID
	AllData   string           `json:"all_data" gorm:"column:all_data;type:text"`        // 全部数据
	CreateAt  time.Time        `json:"create_at" gorm:"column:create_at;autoCreateTime"` // 创建时间
	UpdateAt  time.Time        `json:"update_at" gorm:"column:update_at;autoUpdateTime"` // 更新时间
	ChannelId int              `json:"channel_id" gorm:"column:channel_id;index"`        // 渠道ID
	Channel   MarketingChannel `json:"channel" gorm:"foreignKey:ChannelId;references:ID"`
}

// TableName 返回表名
func (GuiyinDeviceBind) TableName() string {
	return "guiyin_device_bind"
}
