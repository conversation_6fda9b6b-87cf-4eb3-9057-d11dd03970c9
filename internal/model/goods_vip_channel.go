package model

import "time"

// GoodsVipChannel VIP商品渠道关联模型
type GoodsVipChannel struct {
	ID         int       `json:"id" gorm:"column:id;primaryKey;autoIncrement"`                   // 主键id
	GoodsID    int       `json:"goods_id" gorm:"primaryKey;column:goods_id;not null"`            // 商品id
	ChannelID  int       `json:"channel_id" gorm:"primaryKey;column:channel_id;type:int(11);"`   // 渠道id
	Version    string    `json:"version" gorm:"column:version;NOT NULL"`                         // 创建版本
	VersionInt int       `json:"version_int" gorm:"column:version_int;default:0;NOT NULL"`       // 版本int值
	CreateAt   time.Time `json:"create_at" gorm:"column:create_at;type:datetime;autoCreateTime"` // 创建时间，默认当前时间
	IsDelete   int8      `json:"is_delete" gorm:"column:is_delete;default:-1;NOT NULL"`          // 是否删除：-1-未删除；1-已删除
	Sort       int       `json:"sort" gorm:"column:sort;default:0;NOT NULL"`                     // 排序;升序排列

	// 关联商品
	GoodsModel GoodsVip `gorm:"foreignKey:GoodsID"`
	// 关联渠道
	MarketingChannelModel MarketingChannel `gorm:"foreignKey:ChannelID"`
}

// TableName 返回表名
func (m *GoodsVipChannel) TableName() string {
	return "goods_vip_channel"
}
