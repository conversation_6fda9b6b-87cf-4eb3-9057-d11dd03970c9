package main

import (
	"chongli/component"
	"chongli/internal/server"
	"github.com/spf13/cobra"
	_ "go.uber.org/automaxprocs"
	"os"
)

var bootStrap *component.BootStrap

func main() {
	// 初始化组件
	bootStrap = component.NewBootStrap()

	rootCmd := &cobra.Command{
		Use:   "chongli",
		Short: "Chongli application server",
	}
	addCommand(rootCmd)
	execute(rootCmd)
}

func execute(rootCmd *cobra.Command) {
	err := rootCmd.Execute()
	if err != nil {
		if bootStrap != nil && bootStrap.Log != nil {
			bootStrap.Log.Error(err.Error())
		}
		os.Exit(1)
	}
}

func addCommand(rootCmd *cobra.Command) {
	// HTTP 服务器命令
	httpCmd := &cobra.Command{
		Use:   "http_start",
		Short: "Start HTTP server",
		Run: func(cmd *cobra.Command, args []string) {
			server.StartHttpServer(bootStrap)
		},
	}

	// Task 服务器命令
	taskCmd := &cobra.Command{
		Use:   "task_start",
		Short: "Start Task server",
		Run: func(cmd *cobra.Command, args []string) {
			server.StartTaskServer(bootStrap)
		},
	}

	rootCmd.AddCommand(httpCmd)
	rootCmd.AddCommand(taskCmd)
}
