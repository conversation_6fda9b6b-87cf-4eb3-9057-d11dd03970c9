package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/volcengine/volc-sdk-golang/service/visual"
)

// var testAk = "AKLTODAzZjNjMzgxYjJlNDRiMzgzZDQ0ODU1YTdhMjlmZjU"
// var testSk = "WkRBME1HSTJPVE14WkRRME5EVTBOamhsTWpCbE1UbG1ZalEzWW1JM05UQQ=="

func TestHsPic(t *testing.T) {
	var testAk = "AKLTODAzZjNjMzgxYjJlNDRiMzgzZDQ0ODU1YTdhMjlmZjU"
	var testSk = "WkRBME1HSTJPVE14WkRRME5EVTBOamhsTWpCbE1UbG1ZalEzWW1JM05UQQ=="
	visual.DefaultInstance.Client.SetAccessKey(testAk)
	visual.DefaultInstance.Client.SetSecretKey(testSk)
	//visual.DefaultInstance.SetRegion("region")
	//visual.DefaultInstance.SetHost("host")
	// visual.DefaultInstance.Client.SetVersion("v1")
	//请求Body(查看接口文档请求参数-请求示例，将请求参数内容复制到此)
	reqBody := map[string]interface{}{
		"req_key":    "seed3l_single_ip",
		"image_urls": []string{"https://musicbox-cdn.51wnl-cq.com/test/5.png"},
		"prompt":     "将图中女孩的衣服换成华丽的汉服",
	}

	resp, status, err := visual.DefaultInstance.CVSync2AsyncSubmitTask(reqBody)
	fmt.Println(status, err)
	b, _ := json.Marshal(resp)
	fmt.Println(string(b))
}

func TestHsPic2(t *testing.T) {
	var testAk = "AKLTODAzZjNjMzgxYjJlNDRiMzgzZDQ0ODU1YTdhMjlmZjU"
	var testSk = "WkRBME1HSTJPVE14WkRRME5EVTBOamhsTWpCbE1UbG1ZalEzWW1JM05UQQ=="
	visual.DefaultInstance.Client.SetAccessKey(testAk)
	visual.DefaultInstance.Client.SetSecretKey(testSk)
	//visual.DefaultInstance.SetRegion("region")
	//visual.DefaultInstance.SetHost("host")

	//请求Body(查看接口文档请求参数-请求示例，将请求参数内容复制到此)
	reqBody := map[string]interface{}{
		"req_key": "seed3l_single_ip",
		"task_id": "4471455986333640582",
		// "req_json": "{\"return_url\":true}",
	}

	resp, status, err := visual.DefaultInstance.CVSync2AsyncGetResult(reqBody)
	fmt.Println(status, err)
	b, _ := json.Marshal(resp)
	fmt.Println(string(b))
	type HsPicResp struct {
		Code int `json:"code"`
		Data struct {
			BinaryDataBase64 []string `json:"binary_data_base64"`
			ImageUrls        []string `json:"image_urls"`
			RespData         string   `json:"resp_data"`
			Status           string   `json:"status"`
		} `json:"data"`
		RequestId   string `json:"request_id"`
		Status      int    `json:"status"`
		TimeElapsed string `json:"time_elapsed"`
	}
	var respStruct HsPicResp
	err = json.Unmarshal(b, &respStruct)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Printf("%+v\n ", respStruct)

	// 解码并保存base64图片
	if len(respStruct.Data.BinaryDataBase64) > 0 {
		fmt.Println("找到", len(respStruct.Data.BinaryDataBase64), "张base64编码的图片")

		// 创建输出目录
		outputDir := "output_images"
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			t.Fatalf("创建输出目录失败: %v", err)
		}

		// 处理每张图片
		for i, base64Data := range respStruct.Data.BinaryDataBase64 {
			// 解码base64数据
			imageData, err := base64.StdEncoding.DecodeString(base64Data)
			if err != nil {
				fmt.Printf("图片 %d Base64解码错误: %v\n", i, err)
				continue
			}

			// 生成带时间戳的文件名，避免覆盖
			timestamp := time.Now().Format("20060102_150405")
			outputFilename := fmt.Sprintf("%s_image_%d.jpg", timestamp, i)
			outputPath := filepath.Join(outputDir, outputFilename)

			// 保存图片文件
			if err := ioutil.WriteFile(outputPath, imageData, 0644); err != nil {
				fmt.Printf("保存图片 %d 失败: %v\n", i, err)
				continue
			}

			fmt.Printf("图片 %d 已保存到: %s\n", i, outputPath)
		}
	} else {
		fmt.Println("未找到base64编码的图片数据")
	}
}
