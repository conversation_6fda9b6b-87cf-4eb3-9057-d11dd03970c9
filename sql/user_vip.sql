-- 用户VIP信息表
CREATE TABLE `user_vip` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `vip_type` tinyint(4) NOT NULL COMMENT '会员类型：1-月卡；2-季卡；3-年卡',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开通时间',
  `renewal_at` datetime DEFAULT NULL COMMENT '续费时间',
  `expire_at` datetime NOT NULL COMMENT '到期时间',
  `is_expire` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '是否已过期：-1-未过期；1-已过期',
  `is_delete` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '是否删除：-1-未删除；1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_vip_type` (`vip_type`),
  KEY `idx_expire_at` (`expire_at`),
  KEY `idx_create_at` (`create_at`),
  KEY `idx_is_delete` (`is_delete`),
  KEY `idx_is_expire` (`is_expire`),
  KEY `idx_user_vip_type` (`user_id`, `vip_type`),
  KEY `idx_user_expire` (`user_id`, `expire_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户VIP信息表';

-- 会员赠送信息表
CREATE TABLE `user_vip_give` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `vip_type` tinyint(4) NOT NULL COMMENT '会员类型：1-月卡；2-季卡；3-年卡',
  `give_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '赠送时间',
  `give_num` bigint(20) NOT NULL DEFAULT '0' COMMENT '赠送钻石数量',
  `is_give` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '是否已赠送：-1-未赠送；1-已赠送',
  `is_delete` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '是否删除：-1-未删除；1-已删除',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_give_at` (`give_at`),
  KEY `idx_create_at` (`create_at`),
  KEY `idx_is_give` (`is_give`),
  KEY `idx_is_delete` (`is_delete`),
  KEY `idx_user_give_status` (`user_id`, `is_give`),
  KEY `idx_user_give_time` (`user_id`, `give_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员赠送信息表';

-- 创建复合索引以优化常用查询
-- 用户VIP表的复合索引
ALTER TABLE `user_vip` ADD INDEX `idx_user_status_expire` (`user_id`, `is_delete`, `expire_at`);
ALTER TABLE `user_vip` ADD INDEX `idx_type_expire_status` (`vip_type`, `expire_at`, `is_delete`);
-- 新增：包含is_expire字段的复合索引
ALTER TABLE `user_vip` ADD INDEX `idx_user_expire_status` (`user_id`, `is_expire`, `is_delete`);
ALTER TABLE `user_vip` ADD INDEX `idx_expire_status_time` (`is_expire`, `is_delete`, `expire_at`);
ALTER TABLE `user_vip` ADD INDEX `idx_user_expire_time` (`user_id`, `is_expire`, `expire_at`);

-- 会员赠送表的复合索引
ALTER TABLE `user_vip_give` ADD INDEX `idx_user_status_give` (`user_id`, `is_delete`, `is_give`);
ALTER TABLE `user_vip_give` ADD INDEX `idx_give_status_time` (`is_give`, `is_delete`, `give_at`);
-- 新增：包含create_at字段的复合索引
ALTER TABLE `user_vip_give` ADD INDEX `idx_user_create_status` (`user_id`, `create_at`, `is_delete`);
ALTER TABLE `user_vip_give` ADD INDEX `idx_create_status_give` (`create_at`, `is_delete`, `is_give`);
ALTER TABLE `user_vip_give` ADD INDEX `idx_user_create_give` (`user_id`, `create_at`, `is_give`);

-- 新增：包含vip_type字段的索引
ALTER TABLE `user_vip_give` ADD INDEX `idx_vip_type` (`vip_type`);
ALTER TABLE `user_vip_give` ADD INDEX `idx_user_vip_type` (`user_id`, `vip_type`);
ALTER TABLE `user_vip_give` ADD INDEX `idx_vip_type_give_status` (`vip_type`, `is_give`, `is_delete`);
ALTER TABLE `user_vip_give` ADD INDEX `idx_user_vip_give_status` (`user_id`, `vip_type`, `is_give`);
ALTER TABLE `user_vip_give` ADD INDEX `idx_vip_type_time` (`vip_type`, `give_at`, `is_delete`);
ALTER TABLE `user_vip_give` ADD INDEX `idx_user_vip_time` (`user_id`, `vip_type`, `give_at`);
