-- =============================================
-- VIP商品相关表结构
-- =============================================

-- 创建 goods_vip 表 (VIP商品表)
CREATE TABLE IF NOT EXISTS `goods_vip` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `goods_id` varchar(255) NOT NULL COMMENT '商品id',
    `title` varchar(255) NOT NULL COMMENT '商品名称',
    `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品价格',
    `vip_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'vip类型;1:月;2:季;3:年',
    `version` varchar(255) NOT NULL COMMENT '创建版本',
    `channel` varchar(255) NOT NULL COMMENT '客户端/渠道',
    `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序;升序排列',
    `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已被删除，0：未删除；1：已删除',
    `monthly_diamond` int(11) NOT NULL DEFAULT '0' COMMENT '月钻石数量',
    `is_display` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否显示，-1：否；1：是',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP商品表';

-- 创建 goods_vip_channel 表 (VIP商品渠道关联表)
CREATE TABLE IF NOT EXISTS `goods_vip_channel` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `goods_id` int(11) NOT NULL COMMENT '商品id',
    `channel_id` int(11) NOT NULL COMMENT '渠道id',
    `version` varchar(255) NOT NULL COMMENT '创建版本',
    `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，默认当前时间',
    `is_delete` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '是否删除：-1-未删除；1-已删除',
    `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序;升序排列',
    PRIMARY KEY (`id`, `goods_id`, `channel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP商品渠道关联表';

-- =============================================
-- 索引创建
-- =============================================

-- goods_vip 表索引
-- 商品ID索引 (用于快速查找特定商品)
CREATE INDEX `idx_goods_vip_goods_id` ON `goods_vip` (`goods_id`);

-- 渠道索引 (用于按渠道筛选商品)
CREATE INDEX `idx_goods_vip_channel` ON `goods_vip` (`channel`);

-- VIP类型索引 (用于按VIP类型筛选)
CREATE INDEX `idx_goods_vip_vip_type` ON `goods_vip` (`vip_type`);

-- 创建版本索引 (用于按版本筛选)
CREATE INDEX `idx_goods_vip_version` ON `goods_vip` (`version`);

-- 删除状态索引 (用于过滤已删除记录)
CREATE INDEX `idx_goods_vip_is_delete` ON `goods_vip` (`is_delete`);

-- 显示状态索引 (用于过滤显示状态)
CREATE INDEX `idx_goods_vip_is_display` ON `goods_vip` (`is_display`);

-- 排序索引 (用于排序查询)
CREATE INDEX `idx_goods_vip_sort` ON `goods_vip` (`sort`);

-- 组合索引：渠道+删除状态+显示状态+排序 (用于前端商品列表查询优化)
CREATE INDEX `idx_goods_vip_channel_delete_display_sort` ON `goods_vip` (`channel`, `is_delete`, `is_display`, `sort`);

-- 组合索引：VIP类型+删除状态+显示状态 (用于按类型筛选有效商品)
CREATE INDEX `idx_goods_vip_type_delete_display` ON `goods_vip` (`vip_type`, `is_delete`, `is_display`);

-- 组合索引：创建版本+删除状态+显示状态 (用于版本兼容性查询)
CREATE INDEX `idx_goods_vip_version_delete_display` ON `goods_vip` (`version`, `is_delete`, `is_display`);

-- goods_vip_channel 表索引
-- 商品ID索引 (用于查找商品关联的渠道)
CREATE INDEX `idx_goods_vip_channel_goods_id` ON `goods_vip_channel` (`goods_id`);

-- 渠道ID索引 (用于查找渠道关联的商品)
CREATE INDEX `idx_goods_vip_channel_channel_id` ON `goods_vip_channel` (`channel_id`);

-- 创建版本索引 (用于版本筛选)
CREATE INDEX `idx_goods_vip_channel_version` ON `goods_vip_channel` (`version`);

-- 删除状态索引 (用于过滤已删除记录)
CREATE INDEX `idx_goods_vip_channel_is_delete` ON `goods_vip_channel` (`is_delete`);

-- 组合索引：商品ID+删除状态 (用于查询商品的有效渠道关联)
CREATE INDEX `idx_goods_vip_channel_goods_delete` ON `goods_vip_channel` (`goods_id`, `is_delete`);

-- 组合索引：渠道ID+删除状态+排序 (用于查询渠道下的有效商品并排序)
CREATE INDEX `idx_goods_vip_channel_channel_delete_sort` ON `goods_vip_channel` (`channel_id`, `is_delete`, `sort`);

-- 组合索引：创建版本+删除状态 (用于版本兼容性查询)
CREATE INDEX `idx_goods_vip_channel_version_delete` ON `goods_vip_channel` (`version`, `is_delete`);

-- 组合索引：商品ID+渠道ID+创建版本+删除状态 (用于复合条件查询优化)
CREATE INDEX `idx_goods_vip_channel_goods_channel_version_delete` ON `goods_vip_channel` (`goods_id`, `channel_id`, `version`, `is_delete`);

-- 外键约束 (如果需要的话，可以取消注释)
-- ALTER TABLE `goods_vip_channel` ADD CONSTRAINT `fk_goods_vip_channel_goods`
--     FOREIGN KEY (`goods_id`) REFERENCES `goods_vip` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
--
-- ALTER TABLE `goods_vip_channel` ADD CONSTRAINT `fk_goods_vip_channel_channel`
--     FOREIGN KEY (`channel_id`) REFERENCES `channel` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;




-- =============================================
-- 支付订单相关表结构
-- =============================================

-- 创建 pay_order 表 (支付订单表)
CREATE TABLE IF NOT EXISTS `pay_order` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `order_id` varchar(36) NOT NULL COMMENT '订单id',
    `wnl_order_id` varchar(36) NOT NULL DEFAULT '' COMMENT '万年历支付成功订单id',
    `order_state` int(11) NOT NULL DEFAULT '-1' COMMENT '订单状态，-1：待支付；1：已支付；2：已过期；3：已退款',
    `goods_id` int(11) NOT NULL COMMENT '商品主键id',
    `goods_middle_id` varchar(36) NOT NULL COMMENT '商品中台id',
    `goods_title` varchar(255) NOT NULL DEFAULT '' COMMENT '商品名称',
    `user_id` bigint(20) NOT NULL COMMENT '用户id',
    `user_device_id` varchar(40) NOT NULL DEFAULT '' COMMENT '用户设备id',
    `user_bind_channel_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户绑定渠道id',
    `user_bind_channel_name` varchar(40) NOT NULL DEFAULT '' COMMENT '用户绑定渠道名称',
    `user_ip` varchar(255) NOT NULL DEFAULT '' COMMENT '用户ip',
    `pay_type` int(11) NOT NULL DEFAULT '-1' COMMENT '支付方式，1：微信；2：支付宝；3：苹果支付；4：沙盒支付',
    `order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单应付金额',
    `pay_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际支付金额',
    `refund_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
    `version` varchar(30) NOT NULL COMMENT 'app版本',
    `channel` varchar(30) NOT NULL COMMENT 'app渠道',
    `pay_at` datetime DEFAULT NULL COMMENT '支付时间',
    `refund_at` datetime DEFAULT NULL COMMENT '退款时间',
    `expire_at` datetime DEFAULT NULL COMMENT '订单过期时间',
    `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `wnl_callback_data` text COMMENT '万年历支付回调数据',
    `remark` varchar(500) NOT NULL DEFAULT '' COMMENT '订单备注',
    `is_delete` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '是否已被删除，-1：未删除；1：已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1250 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付订单表';

-- =============================================
-- pay_order 表索引
-- =============================================

-- 单列索引
-- 万年历订单ID索引 (用于支付回调查询)
CREATE INDEX `idx_pay_order_wnl_order_id` ON `pay_order` (`wnl_order_id`);

-- 订单状态索引 (用于按状态筛选订单)
CREATE INDEX `idx_pay_order_order_state` ON `pay_order` (`order_state`);

-- 商品ID索引 (用于查询商品的订单)
CREATE INDEX `idx_pay_order_goods_id` ON `pay_order` (`goods_id`);

-- 商品中台ID索引 (用于查询商品中台的订单)
CREATE INDEX `idx_pay_order_goods_middle_id` ON `pay_order` (`goods_middle_id`);

-- 用户ID索引 (用于查询用户的订单)
CREATE INDEX `idx_pay_order_user_id` ON `pay_order` (`user_id`);

-- 用户设备ID索引 (用于设备相关查询)
CREATE INDEX `idx_pay_order_user_device_id` ON `pay_order` (`user_device_id`);

-- 用户绑定渠道ID索引 (用于渠道绑定查询)
CREATE INDEX `idx_pay_order_user_bind_channel_id` ON `pay_order` (`user_bind_channel_id`);

-- 用户绑定渠道名称索引 (用于渠道名称查询)
CREATE INDEX `idx_pay_order_user_bind_channel_name` ON `pay_order` (`user_bind_channel_name`);

-- 用户IP索引 (用于IP相关查询和风控)
CREATE INDEX `idx_pay_order_user_ip` ON `pay_order` (`user_ip`);

-- 支付方式索引 (用于按支付方式筛选)
CREATE INDEX `idx_pay_order_pay_type` ON `pay_order` (`pay_type`);

-- 版本索引 (用于按版本筛选订单)
CREATE INDEX `idx_pay_order_version` ON `pay_order` (`version`);

-- 渠道索引 (用于按渠道筛选订单)
CREATE INDEX `idx_pay_order_channel` ON `pay_order` (`channel`);

-- 支付时间索引 (用于按支付时间排序和筛选)
CREATE INDEX `idx_pay_order_pay_at` ON `pay_order` (`pay_at`);

-- 退款时间索引 (用于退款相关查询)
CREATE INDEX `idx_pay_order_refund_at` ON `pay_order` (`refund_at`);

-- 过期时间索引 (用于查询过期订单)
CREATE INDEX `idx_pay_order_expire_at` ON `pay_order` (`expire_at`);

-- 创建时间索引 (用于按创建时间排序和筛选)
CREATE INDEX `idx_pay_order_create_at` ON `pay_order` (`create_at`);

-- 删除状态索引 (用于过滤已删除记录)
CREATE INDEX `idx_pay_order_is_delete` ON `pay_order` (`is_delete`);

-- 组合索引
-- 用户ID+订单状态+删除状态 (用于查询用户的有效订单)
CREATE INDEX `idx_pay_order_user_state_delete` ON `pay_order` (`user_id`, `order_state`, `is_delete`);

-- 用户ID+删除状态+创建时间 (用于查询用户订单列表并按时间排序)
CREATE INDEX `idx_pay_order_user_delete_create` ON `pay_order` (`user_id`, `is_delete`, `create_at`);

-- 商品ID+订单状态+删除状态 (用于查询商品的有效订单)
CREATE INDEX `idx_pay_order_goods_state_delete` ON `pay_order` (`goods_id`, `order_state`, `is_delete`);

-- 商品中台ID+订单状态+删除状态 (用于查询商品中台的有效订单)
CREATE INDEX `idx_pay_order_goods_middle_state_delete` ON `pay_order` (`goods_middle_id`, `order_state`, `is_delete`);

-- 渠道+订单状态+删除状态+创建时间 (用于渠道订单统计)
CREATE INDEX `idx_pay_order_channel_state_delete_create` ON `pay_order` (`channel`, `order_state`, `is_delete`, `create_at`);

-- 支付方式+订单状态+删除状态 (用于支付方式统计)
CREATE INDEX `idx_pay_order_pay_type_state_delete` ON `pay_order` (`pay_type`, `order_state`, `is_delete`);

-- 版本+渠道+订单状态+删除状态 (用于版本渠道分析)
CREATE INDEX `idx_pay_order_version_channel_state_delete` ON `pay_order` (`version`, `channel`, `order_state`, `is_delete`);

-- 订单状态+支付时间+删除状态 (用于已支付订单按时间查询)
CREATE INDEX `idx_pay_order_state_pay_at_delete` ON `pay_order` (`order_state`, `pay_at`, `is_delete`);

-- 订单状态+过期时间+删除状态 (用于查询过期的待支付订单)
CREATE INDEX `idx_pay_order_state_expire_delete` ON `pay_order` (`order_state`, `expire_at`, `is_delete`);

-- 用户设备ID+订单状态+删除状态 (用于设备订单查询)
CREATE INDEX `idx_pay_order_device_state_delete` ON `pay_order` (`user_device_id`, `order_state`, `is_delete`);

-- 用户绑定渠道ID+订单状态+删除状态 (用于渠道绑定订单查询)
CREATE INDEX `idx_pay_order_bind_channel_id_state_delete` ON `pay_order` (`user_bind_channel_id`, `order_state`, `is_delete`);

-- 用户绑定渠道名称+订单状态+删除状态 (用于渠道名称订单查询)
CREATE INDEX `idx_pay_order_bind_channel_name_state_delete` ON `pay_order` (`user_bind_channel_name`, `order_state`, `is_delete`);

-- 用户IP+订单状态+删除状态 (用于IP风控和订单查询)
CREATE INDEX `idx_pay_order_user_ip_state_delete` ON `pay_order` (`user_ip`, `order_state`, `is_delete`);

-- 订单状态+退款时间+删除状态 (用于退款订单查询)
CREATE INDEX `idx_pay_order_state_refund_delete` ON `pay_order` (`order_state`, `refund_at`, `is_delete`);

-- 支付方式+支付时间+删除状态 (用于支付方式时间分析)
CREATE INDEX `idx_pay_order_pay_type_pay_at_delete` ON `pay_order` (`pay_type`, `pay_at`, `is_delete`);

-- 用户绑定渠道ID+渠道名称+删除状态 (用于渠道一致性检查)
CREATE INDEX `idx_pay_order_bind_channel_id_name_delete` ON `pay_order` (`user_bind_channel_id`, `user_bind_channel_name`, `is_delete`);

-- 用户IP+创建时间+删除状态 (用于IP访问频率分析)
CREATE INDEX `idx_pay_order_user_ip_create_delete` ON `pay_order` (`user_ip`, `create_at`, `is_delete`);

-- 用户ID+用户IP+订单状态 (用于用户行为分析)
CREATE INDEX `idx_pay_order_user_ip_state` ON `pay_order` (`user_id`, `user_ip`, `order_state`);

-- 外键约束 (如果需要的话，可以取消注释)
-- ALTER TABLE `pay_order` ADD CONSTRAINT `fk_pay_order_user`
--     FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- =============================================
-- pay_order 表数据插入
-- =============================================

-- 插入支付订单数据，包含所有用户渠道、商品、绑定渠道、支付方式和支付状态的组合
INSERT INTO `pay_order` (`order_id`, `wnl_order_id`, `order_state`, `goods_id`, `goods_middle_id`, `goods_title`, `user_id`, `user_device_id`, `user_bind_channel_id`, `user_bind_channel_name`, `user_ip`, `pay_type`, `order_amount`, `pay_amount`, `refund_amount`, `version`, `channel`, `pay_at`, `refund_at`, `expire_at`, `create_at`, `update_at`, `wnl_callback_data`, `remark`, `is_delete`) VALUES

-- 月卡商品 + 各种渠道 + 各种支付方式 + 各种状态
-- Youloft_IOS渠道
('ORD-001-001', 'WNL-001-001', 1, 1, 'test_wnl_id_1', '月卡', 115, 'cf37a53f34354b44b8d1d09e636bca2b', 1, '默认渠道', '**************', 1, 28.00, 28.00, 0.00, '1.0.0', 'Youloft_IOS', '2025-07-25 10:00:00', NULL, NULL, '2025-07-25 09:00:00', '2025-07-25 10:00:00', '{"status":"success","trade_no":"wx001"}', '微信支付成功', -1),
('ORD-001-002', 'WNL-001-002', 1, 1, 'test_wnl_id_1', '月卡', 116, '6c661969b7eb454ea23c1fdbded5b0b8', 11, '抖音渠道', '**************', 2, 28.00, 28.00, 0.00, '1.0.0', 'Youloft_IOS', '2025-07-25 10:05:00', NULL, NULL, '2025-07-25 09:05:00', '2025-07-25 10:05:00', '{"status":"success","trade_no":"ali001"}', '支付宝支付成功', -1),
('ORD-001-003', 'WNL-001-003', 1, 1, 'test_wnl_id_1', '月卡', 122, '15295a8f8de24bd98b53bab7c9f26294', 13, '快手渠道', '106.54.229.89', 3, 28.00, 28.00, 0.00, '1.0.0', 'Youloft_IOS', '2025-07-25 10:10:00', NULL, NULL, '2025-07-25 09:10:00', '2025-07-25 10:10:00', '{"status":"success","transaction_id":"apple001"}', '苹果支付成功', -1),
('ORD-001-004', '', -1, 1, 'test_wnl_id_1', '月卡', 123, '3bb7855c74e5413a886677a92ea79bde', 1, '默认渠道', '106.54.230.45', 4, 28.00, 0.00, 0.00, '1.0.0', 'Youloft_IOS', NULL, NULL, '2025-07-25 11:00:00', '2025-07-25 09:15:00', '2025-07-25 09:15:00', '', '待支付订单', -1),

-- Youloft_Android渠道
('ORD-001-005', 'WNL-001-005', 2, 1, 'test_wnl_id_1', '月卡', 129, '157287e10d3443fe952dda5f82529e0c', 11, '抖音渠道', '106.54.231.78', 1, 28.00, 0.00, 0.00, '1.0.0', 'Youloft_Android', NULL, NULL, '2025-07-25 09:20:00', '2025-07-25 09:20:00', '2025-07-25 09:20:00', '', '订单已过期', -1),
('ORD-001-006', 'WNL-001-006', 3, 1, 'test_wnl_id_1', '月卡', 130, '550e8400-e29b-41d4-a716-446655440001', 13, '快手渠道', '106.54.232.112', 2, 28.00, 28.00, 28.00, '1.0.0', 'Youloft_Android', '2025-07-25 10:20:00', '2025-07-25 11:20:00', NULL, '2025-07-25 09:20:00', '2025-07-25 11:20:00', '{"status":"refunded","refund_id":"ref001"}', '用户申请退款', -1),

-- Youloft_Android_huawei渠道
('ORD-001-007', 'WNL-001-007', 1, 1, 'test_wnl_id_1', '月卡', 130, '550e8400-e29b-41d4-a716-446655440001', 1, '默认渠道', '**************', 3, 28.00, 28.00, 0.00, '1.0.0', 'Youloft_Android_huawei', '2025-07-25 10:25:00', NULL, NULL, '2025-07-25 09:25:00', '2025-07-25 10:25:00', '{"status":"success","transaction_id":"apple002"}', '苹果支付成功', -1),
('ORD-001-008', '', -1, 1, 'test_wnl_id_1', '月卡', 131, '550e8400-e29b-41d4-a716-446655440002', 11, '抖音渠道', '**************', 4, 28.00, 0.00, 0.00, '1.0.0', 'Youloft_Android_huawei', NULL, NULL, '2025-07-25 11:30:00', '2025-07-25 09:30:00', '2025-07-25 09:30:00', '', '沙盒支付待支付', -1),

-- Youloft_Android_xiaomi渠道
('ORD-001-009', 'WNL-001-009', 1, 1, 'test_wnl_id_1', '月卡', 131, '550e8400-e29b-41d4-a716-446655440002', 13, '快手渠道', '106.54.229.89', 1, 28.00, 28.00, 0.00, '1.0.0', 'Youloft_Android_xiaomi', '2025-07-25 10:35:00', NULL, NULL, '2025-07-25 09:35:00', '2025-07-25 10:35:00', '{"status":"success","trade_no":"wx002"}', '微信支付成功', -1),
('ORD-001-010', 'WNL-001-010', 2, 1, 'test_wnl_id_1', '月卡', 136, '550e8400-e29b-41d4-a716-446655440007', 1, '默认渠道', '106.54.233.67', 2, 28.00, 0.00, 0.00, '1.0.0', 'Youloft_Android_xiaomi', NULL, NULL, '2025-07-25 09:40:00', '2025-07-25 09:40:00', '2025-07-25 09:40:00', '', '订单已过期', -1),

-- Youloft_Android_vivo渠道
('ORD-001-011', 'WNL-001-011', 1, 1, 'test_wnl_id_1', '月卡', 133, '550e8400-e29b-41d4-a716-446655440004', 11, '抖音渠道', '106.54.230.45', 3, 28.00, 28.00, 0.00, '1.0.0', 'Youloft_Android_vivo', '2025-07-25 10:45:00', NULL, NULL, '2025-07-25 09:45:00', '2025-07-25 10:45:00', '{"status":"success","transaction_id":"apple003"}', '苹果支付成功', -1),
('ORD-001-012', 'WNL-001-012', 3, 1, 'test_wnl_id_1', '月卡', 134, '550e8400-e29b-41d4-a716-446655440005', 13, '快手渠道', '106.54.231.78', 4, 28.00, 28.00, 28.00, '1.0.0', 'Youloft_Android_vivo', '2025-07-25 10:50:00', '2025-07-25 11:50:00', NULL, '2025-07-25 09:50:00', '2025-07-25 11:50:00', '{"status":"refunded","refund_id":"ref002"}', '沙盒支付退款', -1),

-- Youloft_Android_honor渠道
('ORD-001-013', '', -1, 1, 'test_wnl_id_1', '月卡', 134, '550e8400-e29b-41d4-a716-446655440005', 1, '默认渠道', '106.54.232.112', 1, 28.00, 0.00, 0.00, '1.0.0', 'Youloft_Android_honor', NULL, NULL, '2025-07-25 11:55:00', '2025-07-25 09:55:00', '2025-07-25 09:55:00', '', '微信待支付', -1),
('ORD-001-014', 'WNL-001-014', 1, 1, 'test_wnl_id_1', '月卡', 139, '550e8400-e29b-41d4-a716-446655440010', 11, '抖音渠道', '106.54.236.58', 2, 28.00, 28.00, 0.00, '1.0.0', 'Youloft_Android_honor', '2025-07-25 11:00:00', NULL, NULL, '2025-07-25 10:00:00', '2025-07-25 11:00:00', '{"status":"success","trade_no":"ali002"}', '支付宝支付成功', -1),

-- Youloft_Android_oppo渠道
('ORD-001-015', 'WNL-001-015', 2, 1, 'test_wnl_id_1', '月卡', 137, '550e8400-e29b-41d4-a716-446655440008', 13, '快手渠道', '106.54.234.134', 3, 28.00, 0.00, 0.00, '1.0.0', 'Youloft_Android_oppo', NULL, NULL, '2025-07-25 10:05:00', '2025-07-25 10:05:00', '2025-07-25 10:05:00', '', '苹果支付过期', -1),
('ORD-001-016', 'WNL-001-016', 3, 1, 'test_wnl_id_1', '月卡', 138, '550e8400-e29b-41d4-a716-446655440009', 1, '默认渠道', '106.54.235.91', 4, 28.00, 28.00, 28.00, '1.0.0', 'Youloft_Android_oppo', '2025-07-25 11:10:00', '2025-07-25 12:10:00', NULL, '2025-07-25 10:10:00', '2025-07-25 12:10:00', '{"status":"refunded","refund_id":"ref003"}', '沙盒支付退款', -1),

-- 季卡商品 + 各种渠道 + 各种支付方式 + 各种状态
-- Youloft_IOS渠道
('ORD-002-001', 'WNL-002-001', 1, 2, 'test_wnl_id_2', '季卡', 115, 'cf37a53f34354b44b8d1d09e636bca2b', 1, '默认渠道', '**************', 1, 68.00, 68.00, 0.00, '1.0.0', 'Youloft_IOS', '2025-07-25 11:15:00', NULL, NULL, '2025-07-25 10:15:00', '2025-07-25 11:15:00', '{"status":"success","trade_no":"wx003"}', '微信支付成功', -1),
('ORD-002-002', 'WNL-002-002', 1, 2, 'test_wnl_id_2', '季卡', 116, '6c661969b7eb454ea23c1fdbded5b0b8', 11, '抖音渠道', '**************', 2, 68.00, 68.00, 0.00, '1.0.0', 'Youloft_IOS', '2025-07-25 11:20:00', NULL, NULL, '2025-07-25 10:20:00', '2025-07-25 11:20:00', '{"status":"success","trade_no":"ali003"}', '支付宝支付成功', -1),
('ORD-002-003', 'WNL-002-003', 1, 2, 'test_wnl_id_2', '季卡', 122, '15295a8f8de24bd98b53bab7c9f26294', 13, '快手渠道', '106.54.229.89', 3, 68.00, 68.00, 0.00, '1.0.0', 'Youloft_IOS', '2025-07-25 11:25:00', NULL, NULL, '2025-07-25 10:25:00', '2025-07-25 11:25:00', '{"status":"success","transaction_id":"apple004"}', '苹果支付成功', -1),
('ORD-002-004', '', -1, 2, 'test_wnl_id_2', '季卡', 123, '3bb7855c74e5413a886677a92ea79bde', 1, '默认渠道', '106.54.230.45', 4, 68.00, 0.00, 0.00, '1.0.0', 'Youloft_IOS', NULL, NULL, '2025-07-25 12:30:00', '2025-07-25 10:30:00', '2025-07-25 10:30:00', '', '沙盒支付待支付', -1),

-- Youloft_Android渠道
('ORD-002-005', 'WNL-002-005', 2, 2, 'test_wnl_id_2', '季卡', 129, '157287e10d3443fe952dda5f82529e0c', 11, '抖音渠道', '106.54.231.78', 1, 68.00, 0.00, 0.00, '1.0.0', 'Youloft_Android', NULL, NULL, '2025-07-25 10:35:00', '2025-07-25 10:35:00', '2025-07-25 10:35:00', '', '微信支付过期', -1),
('ORD-002-006', 'WNL-002-006', 3, 2, 'test_wnl_id_2', '季卡', 130, '550e8400-e29b-41d4-a716-446655440001', 13, '快手渠道', '106.54.232.112', 2, 68.00, 68.00, 68.00, '1.0.0', 'Youloft_Android', '2025-07-25 11:35:00', '2025-07-25 12:35:00', NULL, '2025-07-25 10:35:00', '2025-07-25 12:35:00', '{"status":"refunded","refund_id":"ref004"}', '支付宝退款', -1),

-- Youloft_Android_huawei渠道
('ORD-002-007', 'WNL-002-007', 1, 2, 'test_wnl_id_2', '季卡', 130, '550e8400-e29b-41d4-a716-446655440001', 1, '默认渠道', '**************', 3, 68.00, 68.00, 0.00, '1.0.0', 'Youloft_Android_huawei', '2025-07-25 11:40:00', NULL, NULL, '2025-07-25 10:40:00', '2025-07-25 11:40:00', '{"status":"success","transaction_id":"apple005"}', '苹果支付成功', -1),
('ORD-002-008', '', -1, 2, 'test_wnl_id_2', '季卡', 131, '550e8400-e29b-41d4-a716-446655440002', 11, '抖音渠道', '**************', 4, 68.00, 0.00, 0.00, '1.0.0', 'Youloft_Android_huawei', NULL, NULL, '2025-07-25 12:45:00', '2025-07-25 10:45:00', '2025-07-25 10:45:00', '', '沙盒支付待支付', -1),

-- Youloft_Android_xiaomi渠道
('ORD-002-009', 'WNL-002-009', 1, 2, 'test_wnl_id_2', '季卡', 131, '550e8400-e29b-41d4-a716-446655440002', 13, '快手渠道', '106.54.229.89', 1, 68.00, 68.00, 0.00, '1.0.0', 'Youloft_Android_xiaomi', '2025-07-25 11:50:00', NULL, NULL, '2025-07-25 10:50:00', '2025-07-25 11:50:00', '{"status":"success","trade_no":"wx004"}', '微信支付成功', -1),
('ORD-002-010', 'WNL-002-010', 2, 2, 'test_wnl_id_2', '季卡', 136, '550e8400-e29b-41d4-a716-446655440007', 1, '默认渠道', '106.54.233.67', 2, 68.00, 0.00, 0.00, '1.0.0', 'Youloft_Android_xiaomi', NULL, NULL, '2025-07-25 10:55:00', '2025-07-25 10:55:00', '2025-07-25 10:55:00', '', '支付宝支付过期', -1),

-- Youloft_Android_vivo渠道
('ORD-002-011', 'WNL-002-011', 1, 2, 'test_wnl_id_2', '季卡', 133, '550e8400-e29b-41d4-a716-446655440004', 11, '抖音渠道', '106.54.230.45', 3, 68.00, 68.00, 0.00, '1.0.0', 'Youloft_Android_vivo', '2025-07-25 12:00:00', NULL, NULL, '2025-07-25 11:00:00', '2025-07-25 12:00:00', '{"status":"success","transaction_id":"apple006"}', '苹果支付成功', -1),
('ORD-002-012', 'WNL-002-012', 3, 2, 'test_wnl_id_2', '季卡', 134, '550e8400-e29b-41d4-a716-446655440005', 13, '快手渠道', '106.54.231.78', 4, 68.00, 68.00, 68.00, '1.0.0', 'Youloft_Android_vivo', '2025-07-25 12:05:00', '2025-07-25 13:05:00', NULL, '2025-07-25 11:05:00', '2025-07-25 13:05:00', '{"status":"refunded","refund_id":"ref005"}', '沙盒支付退款', -1),

-- Youloft_Android_honor渠道
('ORD-002-013', '', -1, 2, 'test_wnl_id_2', '季卡', 134, '550e8400-e29b-41d4-a716-446655440005', 1, '默认渠道', '106.54.232.112', 1, 68.00, 0.00, 0.00, '1.0.0', 'Youloft_Android_honor', NULL, NULL, '2025-07-25 13:10:00', '2025-07-25 11:10:00', '2025-07-25 11:10:00', '', '微信待支付', -1),
('ORD-002-014', 'WNL-002-014', 1, 2, 'test_wnl_id_2', '季卡', 139, '550e8400-e29b-41d4-a716-446655440010', 11, '抖音渠道', '106.54.236.58', 2, 68.00, 68.00, 0.00, '1.0.0', 'Youloft_Android_honor', '2025-07-25 12:15:00', NULL, NULL, '2025-07-25 11:15:00', '2025-07-25 12:15:00', '{"status":"success","trade_no":"ali004"}', '支付宝支付成功', -1),

-- Youloft_Android_oppo渠道
('ORD-002-015', 'WNL-002-015', 2, 2, 'test_wnl_id_2', '季卡', 137, '550e8400-e29b-41d4-a716-446655440008', 13, '快手渠道', '106.54.234.134', 3, 68.00, 0.00, 0.00, '1.0.0', 'Youloft_Android_oppo', NULL, NULL, '2025-07-25 11:20:00', '2025-07-25 11:20:00', '2025-07-25 11:20:00', '', '苹果支付过期', -1),
('ORD-002-016', 'WNL-002-016', 3, 2, 'test_wnl_id_2', '季卡', 138, '550e8400-e29b-41d4-a716-446655440009', 1, '默认渠道', '106.54.235.91', 4, 68.00, 68.00, 68.00, '1.0.0', 'Youloft_Android_oppo', '2025-07-25 12:25:00', '2025-07-25 13:25:00', NULL, '2025-07-25 11:25:00', '2025-07-25 13:25:00', '{"status":"refunded","refund_id":"ref006"}', '沙盒支付退款', -1),

-- 年卡商品 + 各种渠道 + 各种支付方式 + 各种状态
-- Youloft_IOS渠道
('ORD-003-001', 'WNL-003-001', 1, 3, 'test_wnl_id_3', '年卡', 115, 'cf37a53f34354b44b8d1d09e636bca2b', 1, '默认渠道', '**************', 1, 198.00, 198.00, 0.00, '1.0.0', 'Youloft_IOS', '2025-07-25 12:30:00', NULL, NULL, '2025-07-25 11:30:00', '2025-07-25 12:30:00', '{"status":"success","trade_no":"wx005"}', '微信支付成功', -1),
('ORD-003-002', 'WNL-003-002', 1, 3, 'test_wnl_id_3', '年卡', 116, '6c661969b7eb454ea23c1fdbded5b0b8', 11, '抖音渠道', '**************', 2, 198.00, 198.00, 0.00, '1.0.0', 'Youloft_IOS', '2025-07-25 12:35:00', NULL, NULL, '2025-07-25 11:35:00', '2025-07-25 12:35:00', '{"status":"success","trade_no":"ali005"}', '支付宝支付成功', -1),
('ORD-003-003', 'WNL-003-003', 1, 3, 'test_wnl_id_3', '年卡', 122, '15295a8f8de24bd98b53bab7c9f26294', 13, '快手渠道', '106.54.229.89', 3, 198.00, 198.00, 0.00, '1.0.0', 'Youloft_IOS', '2025-07-25 12:40:00', NULL, NULL, '2025-07-25 11:40:00', '2025-07-25 12:40:00', '{"status":"success","transaction_id":"apple007"}', '苹果支付成功', -1),
('ORD-003-004', '', -1, 3, 'test_wnl_id_3', '年卡', 123, '3bb7855c74e5413a886677a92ea79bde', 1, '默认渠道', '106.54.230.45', 4, 198.00, 0.00, 0.00, '1.0.0', 'Youloft_IOS', NULL, NULL, '2025-07-25 13:45:00', '2025-07-25 11:45:00', '2025-07-25 11:45:00', '', '沙盒支付待支付', -1),

-- Youloft_Android渠道
('ORD-003-005', 'WNL-003-005', 2, 3, 'test_wnl_id_3', '年卡', 129, '157287e10d3443fe952dda5f82529e0c', 11, '抖音渠道', '106.54.231.78', 1, 198.00, 0.00, 0.00, '1.0.0', 'Youloft_Android', NULL, NULL, '2025-07-25 11:50:00', '2025-07-25 11:50:00', '2025-07-25 11:50:00', '', '微信支付过期', -1),
('ORD-003-006', 'WNL-003-006', 3, 3, 'test_wnl_id_3', '年卡', 130, '550e8400-e29b-41d4-a716-446655440001', 13, '快手渠道', '106.54.232.112', 2, 198.00, 198.00, 198.00, '1.0.0', 'Youloft_Android', '2025-07-25 12:50:00', '2025-07-25 13:50:00', NULL, '2025-07-25 11:50:00', '2025-07-25 13:50:00', '{"status":"refunded","refund_id":"ref007"}', '支付宝退款', -1),

-- Youloft_Android_huawei渠道
('ORD-003-007', 'WNL-003-007', 1, 3, 'test_wnl_id_3', '年卡', 130, '550e8400-e29b-41d4-a716-446655440001', 1, '默认渠道', '**************', 3, 198.00, 198.00, 0.00, '1.0.0', 'Youloft_Android_huawei', '2025-07-25 12:55:00', NULL, NULL, '2025-07-25 11:55:00', '2025-07-25 12:55:00', '{"status":"success","transaction_id":"apple008"}', '苹果支付成功', -1),
('ORD-003-008', '', -1, 3, 'test_wnl_id_3', '年卡', 131, '550e8400-e29b-41d4-a716-446655440002', 11, '抖音渠道', '**************', 4, 198.00, 0.00, 0.00, '1.0.0', 'Youloft_Android_huawei', NULL, NULL, '2025-07-25 14:00:00', '2025-07-25 12:00:00', '2025-07-25 12:00:00', '', '沙盒支付待支付', -1),

-- Youloft_Android_xiaomi渠道
('ORD-003-009', 'WNL-003-009', 1, 3, 'test_wnl_id_3', '年卡', 131, '550e8400-e29b-41d4-a716-446655440002', 13, '快手渠道', '106.54.229.89', 1, 198.00, 198.00, 0.00, '1.0.0', 'Youloft_Android_xiaomi', '2025-07-25 13:05:00', NULL, NULL, '2025-07-25 12:05:00', '2025-07-25 13:05:00', '{"status":"success","trade_no":"wx006"}', '微信支付成功', -1),
('ORD-003-010', 'WNL-003-010', 2, 3, 'test_wnl_id_3', '年卡', 136, '550e8400-e29b-41d4-a716-446655440007', 1, '默认渠道', '106.54.233.67', 2, 198.00, 0.00, 0.00, '1.0.0', 'Youloft_Android_xiaomi', NULL, NULL, '2025-07-25 12:10:00', '2025-07-25 12:10:00', '2025-07-25 12:10:00', '', '支付宝支付过期', -1),

-- Youloft_Android_vivo渠道
('ORD-003-011', 'WNL-003-011', 1, 3, 'test_wnl_id_3', '年卡', 133, '550e8400-e29b-41d4-a716-446655440004', 11, '抖音渠道', '106.54.230.45', 3, 198.00, 198.00, 0.00, '1.0.0', 'Youloft_Android_vivo', '2025-07-25 13:15:00', NULL, NULL, '2025-07-25 12:15:00', '2025-07-25 13:15:00', '{"status":"success","transaction_id":"apple009"}', '苹果支付成功', -1),
('ORD-003-012', 'WNL-003-012', 3, 3, 'test_wnl_id_3', '年卡', 134, '550e8400-e29b-41d4-a716-446655440005', 13, '快手渠道', '106.54.231.78', 4, 198.00, 198.00, 198.00, '1.0.0', 'Youloft_Android_vivo', '2025-07-25 13:20:00', '2025-07-25 14:20:00', NULL, '2025-07-25 12:20:00', '2025-07-25 14:20:00', '{"status":"refunded","refund_id":"ref008"}', '沙盒支付退款', -1),

-- Youloft_Android_honor渠道
('ORD-003-013', '', -1, 3, 'test_wnl_id_3', '年卡', 134, '550e8400-e29b-41d4-a716-446655440005', 1, '默认渠道', '106.54.232.112', 1, 198.00, 0.00, 0.00, '1.0.0', 'Youloft_Android_honor', NULL, NULL, '2025-07-25 14:25:00', '2025-07-25 12:25:00', '2025-07-25 12:25:00', '', '微信待支付', -1),
('ORD-003-014', 'WNL-003-014', 1, 3, 'test_wnl_id_3', '年卡', 139, '550e8400-e29b-41d4-a716-446655440010', 11, '抖音渠道', '106.54.236.58', 2, 198.00, 198.00, 0.00, '1.0.0', 'Youloft_Android_honor', '2025-07-25 13:30:00', NULL, NULL, '2025-07-25 12:30:00', '2025-07-25 13:30:00', '{"status":"success","trade_no":"ali006"}', '支付宝支付成功', -1),

-- Youloft_Android_oppo渠道
('ORD-003-015', 'WNL-003-015', 2, 3, 'test_wnl_id_3', '年卡', 137, '550e8400-e29b-41d4-a716-446655440008', 13, '快手渠道', '106.54.234.134', 3, 198.00, 0.00, 0.00, '1.0.0', 'Youloft_Android_oppo', NULL, NULL, '2025-07-25 12:35:00', '2025-07-25 12:35:00', '2025-07-25 12:35:00', '', '苹果支付过期', -1),
('ORD-003-016', 'WNL-003-016', 3, 3, 'test_wnl_id_3', '年卡', 138, '550e8400-e29b-41d4-a716-446655440009', 1, '默认渠道', '106.54.235.91', 4, 198.00, 198.00, 198.00, '1.0.0', 'Youloft_Android_oppo', '2025-07-25 13:40:00', '2025-07-25 14:40:00', NULL, '2025-07-25 12:40:00', '2025-07-25 14:40:00', '{"status":"refunded","refund_id":"ref009"}', '沙盒支付退款', -1);
