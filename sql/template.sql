-- 模板分类表
CREATE TABLE `template_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序，数字越小越靠前',
  `max_version` varchar(100) NOT NULL COMMENT '最大适用版本',
  `max_version_int` bigint(20) NOT NULL COMMENT '最大适用版本int值',
  `is_active` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否启用：1-启用；-1-禁用',
  `is_delete` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '是否删除：-1-未删除；1-已删除',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `main_class` tinyint(4) DEFAULT '0' NULL COMMENT '主分类',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_sort` (`sort`),
  KEY `idx_max_version` (`max_version`),
  KEY `idx_max_version_int` (`max_version_int`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_delete` (`is_delete`),
  KEY `idx_create_at` (`create_at`),
  KEY `idx_update_at` (`update_at`),
  KEY `idx_main_class` (`main_class`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板分类表';

-- 创建复合索引以优化常用查询
ALTER TABLE `template_category` ADD INDEX `idx_active_delete_sort` (`is_active`, `is_delete`, `sort`);
ALTER TABLE `template_category` ADD INDEX `idx_delete_active_create` (`is_delete`, `is_active`, `create_at`);
ALTER TABLE `template_category` ADD INDEX `idx_max_version_active_delete` (`max_version`, `is_active`, `is_delete`);
ALTER TABLE `template_category` ADD INDEX `idx_max_version_int_active_delete` (`max_version_int`, `is_active`, `is_delete`);
ALTER TABLE `template_category` ADD INDEX `idx_name_max_version` (`name`, `max_version`);
ALTER TABLE `template_category` ADD INDEX `idx_name_max_version_int` (`name`, `max_version_int`);
ALTER TABLE `template_category` ADD INDEX `idx_max_version_max_version_int` (`max_version`, `max_version_int`);
ALTER TABLE `template_category` ADD INDEX `idx_main_class_active_delete` (`main_class`, `is_active`, `is_delete`);
ALTER TABLE `template_category` ADD INDEX `idx_main_class_sort` (`main_class`, `sort`);

-- 插入默认分类数据
INSERT INTO `template_category` (`name`, `sort`, `max_version`, `max_version_int`, `is_active`, `is_delete`, `main_class`) VALUES
('中国风', 1, '2.0.0', 2000000, 1, -1, 1),
('冰雪世界', 2, '2.0.0', 2000000, 1, -1, 1),
('婚礼', 3, '2.0.0', 2000000, 1, -1, 1),
('穿越古代', 4, '2.0.0', 2000000, 1, -1, 1);

-- Banner表
CREATE TABLE `banner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `image` varchar(500) NOT NULL COMMENT '图片URL',
  `video` varchar(500) NOT NULL COMMENT '视频URL',
  `desc` text NOT NULL COMMENT '描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，数字越小越靠前',
  `is_active` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否启用：1-启用；-1-禁用',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '是否删除：-1-未删除；1-已删除',
  `max_version` varchar(100) NOT NULL COMMENT '最大版本号',
  `max_version_int` bigint(20) NOT NULL COMMENT '最大版本号整数值',
  PRIMARY KEY (`id`),
  KEY `idx_title` (`title`),
  KEY `idx_sort` (`sort`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_delete` (`is_delete`),
  KEY `idx_create_at` (`create_at`),
  KEY `idx_update_at` (`update_at`),
  KEY `idx_max_version` (`max_version`),
  KEY `idx_max_version_int` (`max_version_int`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Banner表';

-- 创建Banner复合索引以优化常用查询
ALTER TABLE `banner` ADD INDEX `idx_active_delete_sort` (`is_active`, `is_delete`, `sort`);
ALTER TABLE `banner` ADD INDEX `idx_delete_active_create` (`is_delete`, `is_active`, `create_at`);
ALTER TABLE `banner` ADD INDEX `idx_max_version_active_delete` (`max_version`, `is_active`, `is_delete`);
ALTER TABLE `banner` ADD INDEX `idx_max_version_int_active_delete` (`max_version_int`, `is_active`, `is_delete`);
ALTER TABLE `banner` ADD INDEX `idx_title_max_version` (`title`, `max_version`);
ALTER TABLE `banner` ADD INDEX `idx_title_max_version_int` (`title`, `max_version_int`);
ALTER TABLE `banner` ADD INDEX `idx_max_version_max_version_int` (`max_version`, `max_version_int`);
