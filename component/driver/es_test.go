package driver

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestInit(t *testing.T) {
	Convey("测试 ES-Init", t, func() {
		drivers := InitDrivers()
		es, err := drivers.GetEsClient()
		So(err, ShouldBeNil)
		So(es, ShouldNotBeNil)
	})
}

func TestCreateIndex(t *testing.T) {
	Convey("测试 ES-CreateIndex", t, func() {
		drivers := InitDrivers()
		es, err := drivers.GetEsClient()
		So(err, ShouldBeNil)
		So(es, ShouldNotBeNil)

		created, err := es.CreateIndex("first_name")
		So(created, ShouldBeTrue)
		So(err, ShouldBeNil)
	})
}

func TestInsertDocument(t *testing.T) {
	Convey("测试 ES-InsertDocument", t, func() {
		drivers := InitDrivers()
		es, err := drivers.GetEsClient()
		So(err, ShouldBeNil)
		So(es, ShouldNotBeNil)

		inserted, err := es.InsertDocument(
			"1",
			"first_name",
			map[string]interface{}{
				"firstName": "James",
				"lastName":  "Bond",
				"age":       21,
			})
		So(inserted, ShouldBeTrue)
		So(err, ShouldBeNil)
	})
}

func TestUpdateDocument(t *testing.T) {
	Convey("测试 ES-UpdateDocument", t, func() {
		drivers := InitDrivers()
		es, err := drivers.GetEsClient()
		So(err, ShouldBeNil)
		So(es, ShouldNotBeNil)

		updated, err := es.UpdateDocument(
			"1",
			"first_name",
			map[string]interface{}{
				"firstName": "Tom",
				"lastName":  "Rose",
				"age":       33,
			},
		)

		So(updated, ShouldBeTrue)
		So(err, ShouldBeNil)
	})
}

func TestGetDocument(t *testing.T) {
	Convey("测试 ES-GetDocument", t, func() {
		drivers := InitDrivers()
		es, err := drivers.GetEsClient()
		So(err, ShouldBeNil)
		So(es, ShouldNotBeNil)

		result, err := es.GetDocument("1", "first_name")
		So(result, ShouldNotBeNil)
		So(err, ShouldBeNil)
	})
}

func TestSearchDocument(t *testing.T) {
	Convey("测试 ES-SearchDocument", t, func() {
		drivers := InitDrivers()
		es, err := drivers.GetEsClient()
		So(err, ShouldBeNil)
		So(es, ShouldNotBeNil)

		result, err := es.SearchDocument(nil, "first_name")
		So(result, ShouldNotBeNil)
		So(err, ShouldBeNil)
	})
}

func TestDeleteDocument(t *testing.T) {
	Convey("测试 ES-DeleteDocument", t, func() {
		drivers := InitDrivers()
		es, err := drivers.GetEsClient()
		So(err, ShouldBeNil)
		So(es, ShouldNotBeNil)

		deleted, err := es.DeleteDocument("1", "first_name")
		So(deleted, ShouldBeTrue)
		So(err, ShouldBeNil)
	})
}
