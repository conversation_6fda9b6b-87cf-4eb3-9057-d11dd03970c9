package driver

import (
	"chongli/component/apollo"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/http"
	"os"
	"sync"
	"time"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
	"github.com/elastic/go-elasticsearch/v7/estransport"
	"github.com/elastic/go-elasticsearch/v7/esutil"
	"golang.org/x/net/context"
)

// esClient singleton of ESClient.
var esClient *EsClient

// EsClient elastic-search client.
type EsClient struct {
	client *elasticsearch.Client
}

// IESClient interfaces of elastic-search client.
type IESClient interface {
	CreateIndex(indexName string) (created bool, err error)
	InsertDocument(id, indexName string, document map[string]interface{}) (inserted bool, err error)
	UpdateDocument(id, indexName string, document map[string]interface{}) (updated bool, err error)
	GetDocument(id, indexName string) (result map[string]interface{}, err error)
	SearchDocument(query interface{}, indexName string) (result map[string]interface{}, err error)
	DeleteDocument(id, indexName string) (deleted bool, err error)

	GetEsSourceClient() *elasticsearch.Client
}

// initEsClient init esClient.
func initEsClient() (err error) {
	es, err := newEsClient()
	if err != nil {
		return err
	}

	esClient = &EsClient{client: es}

	return
}

// newEsClient new elastic-search client.
func newEsClient() (es *elasticsearch.Client, err error) {
	// get apollo config.
	apolloConfig := apollo.GetApolloConfig()

	es, err = elasticsearch.NewClient(elasticsearch.Config{
		Addresses: []string{apolloConfig.EsAddr},
		Username:  apolloConfig.EsUser,
		Password:  apolloConfig.EsPass,
		Transport: &http.Transport{
			MaxIdleConnsPerHost:   10,
			ResponseHeaderTimeout: 3 * time.Second,
			DialContext:           (&net.Dialer{Timeout: 3 * time.Second}).DialContext,
			TLSClientConfig: &tls.Config{
				MinVersion: tls.VersionTLS10,
			},
		},
		Logger: &estransport.ColorLogger{Output: os.Stdout},
	})

	return
}

// getEsClient get elastic-search client.
func getEsClient() (ec IESClient, err error) {
	if esClient == nil {
		return nil, errors.New("esClient uninitialized")
	}

	if esClient.client == nil {
		// execution once.
		var once sync.Once
		once.Do(func() {
			es, _ := newEsClient()
			esClient.client = es
		})
	}

	return esClient, nil
}

// CreateIndex create index of elastic-search.
func (esClient *EsClient) CreateIndex(indexName string) (created bool, err error) {
	if esClient.client == nil {
		return false, errors.New("esClient.client is a null-pointer")
	}

	req := esapi.IndicesCreateRequest{
		Index: indexName,
	}

	res, err := req.Do(context.Background(), esClient.client)
	if err != nil {
		return false, fmt.Errorf("create index failed: %s", err.Error())
	}
	defer res.Body.Close()

	if res.IsError() {
		return false, fmt.Errorf("create index failed: %s", res.String())
	}

	return true, nil
}

// InsertDocument insert document into elastic-search.
func (esClient *EsClient) InsertDocument(id, indexName string, document map[string]interface{}) (inserted bool, err error) {
	if esClient.client == nil {
		return false, errors.New("esClient.client is a null-pointer")
	}

	req := esapi.IndexRequest{
		Index:      indexName,
		DocumentID: id,
		Body:       esutil.NewJSONReader(document),
		Refresh:    "true",
	}

	res, err := req.Do(context.Background(), esClient.client)
	if err != nil {
		return false, fmt.Errorf("insert document failed: %s", err.Error())
	}
	defer res.Body.Close()

	if res.IsError() {
		return false, fmt.Errorf("insert document failed: %s", res.String())
	}

	return true, nil
}

// UpdateDocument update document in elastic-search.
func (esClient *EsClient) UpdateDocument(id, indexName string, document map[string]interface{}) (updated bool, err error) {
	if esClient.client == nil {
		return false, errors.New("esClient.client is a null-pointer")
	}

	doc, err := json.Marshal(document)
	if err != nil {
		return false, fmt.Errorf("document marshall error: %s", err.Error())
	}

	req := esapi.UpdateRequest{
		Index:      indexName,
		DocumentID: id,
		Body:       esutil.NewJSONReader(json.RawMessage(`{"doc": ` + string(doc) + `}`)),
	}

	res, err := req.Do(context.Background(), esClient.client)
	if err != nil {
		return false, fmt.Errorf("update document failed: %s", err.Error())
	}
	defer res.Body.Close()

	if res.IsError() {
		return false, fmt.Errorf("update document failed: %s", res.String())
	}

	return true, nil
}

// GetDocument get document from elastic-search.
func (esClient *EsClient) GetDocument(id, indexName string) (result map[string]interface{}, err error) {
	if esClient.client == nil {
		return nil, errors.New("esClient.client is a null-pointer")
	}

	req := esapi.GetRequest{
		Index:      indexName,
		DocumentID: id,
	}

	res, err := req.Do(context.Background(), esClient.client)
	if err != nil {
		return nil, fmt.Errorf("get document failed: %s", err.Error())
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("get document failed: %s", res.String())
	}

	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("Error parsing the response body: %s", err.Error())
	}

	return
}

// SearchDocument search document in elastic-search.
//
// if query is nil, then query all documents.
func (esClient *EsClient) SearchDocument(query interface{}, indexName string) (result map[string]interface{}, err error) {
	if esClient.client == nil {
		return nil, errors.New("esClient.client is a null-pointer")
	}

	if query == nil {
		query = map[string]interface{}{
			"query": map[string]interface{}{
				"match_all": map[string]interface{}{},
			},
		}
	}

	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  esutil.NewJSONReader(query),
	}

	res, err := req.Do(context.Background(), esClient.client)
	if err != nil {
		return nil, fmt.Errorf("search document failed: %s", err.Error())
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("search document failed: %s", res.String())
	}

	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("Error parsing the response body: %s", err.Error())
	}

	return
}

// DeleteDocument delete document from elastic-search.
func (esClient *EsClient) DeleteDocument(id, indexName string) (deleted bool, err error) {
	if esClient.client == nil {
		return false, errors.New("esClient.client is a null-pointer")
	}

	req := esapi.DeleteRequest{
		Index:      indexName,
		DocumentID: id,
	}

	res, err := req.Do(context.Background(), esClient.client)
	if err != nil {
		return false, fmt.Errorf("delete document failed: %s", err.Error())
	}
	defer res.Body.Close()

	if res.IsError() {
		return false, fmt.Errorf("delete document failed: %s", res.String())
	}

	return true, nil
}

// GetEsSourceClient get elastic-search source client.
func (esClient *EsClient) GetEsSourceClient() *elasticsearch.Client {
	return esClient.client
}
