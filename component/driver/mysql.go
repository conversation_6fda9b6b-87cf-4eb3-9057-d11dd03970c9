package driver

import (
	"chongli/component/apollo"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	mylog "chongli/pkg/logger"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// mysqlDb the singleton of MySQL.
var mysqlDb *gorm.DB

// initMysqlDb init the singleton of MySQL.
func initMysqlDb() *gorm.DB {
	mysqlDb = newMysqlDb()
	return mysqlDb
}

// getMysqlDb get the singleton of MySQL.
func getMysqlDb() *gorm.DB {
	if mysqlDb == nil {
		// execution once.
		var once sync.Once
		once.Do(func() {
			mysqlDb = newMysqlDb()
		})
	}
	return mysqlDb
}

// mysqlDbTxBegin get the singleton of MySQL by transaction.
func mysqlDbTxBegin() *gorm.DB {
	return getMysqlDb().Begin()
}

// newMysqlDb create a MySQL instance.
func newMysqlDb() (mysqlDb *gorm.DB) {
	apolloConfig := apollo.GetApolloConfig()
	mysqlDb, _err := gorm.Open(mysql.New(mysql.Config{
		DSN:                       apolloConfig.MySQLDsn, // DSN data source name
		DefaultStringSize:         256,                   // string 类型字段的默认长度
		SkipInitializeWithVersion: false,                 // 根据当前 MySQL 版本自动配置
	}), &gorm.Config{
		Logger: logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
			logger.Config{
				SlowThreshold: time.Second, // 慢 SQL 阈值
				LogLevel:      logger.Info, // Log level,日志级别
				Colorful:      false,       // 禁用彩色打印
			},
		),
		SkipDefaultTransaction: true, // 禁用全局事务
	})
	if _err != nil {
		fmt.Printf("mysql异常:%v\r\n", _err)
		panic(fmt.Sprintf("mysql异常:%v\r\n", _err))
	}
	db, _err := mysqlDb.DB()
	if _err != nil {
		fmt.Printf("mysql db.DB 异常:%v\r\n", _err)
		panic(fmt.Sprintf("mysql db.DB异常:%v\r\n", _err))
	}
	db.SetMaxIdleConns(10)
	db.SetMaxOpenConns(20)
	db.SetConnMaxLifetime(30 * time.Second)
	mylog.Log().Println("mysql init success")

	return
}
