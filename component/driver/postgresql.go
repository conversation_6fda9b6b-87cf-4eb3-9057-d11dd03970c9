package driver

import (
	"chongli/component/apollo"
	"fmt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"sync"
	"time"

	loggerpkg "chongli/pkg/logger"

	"gorm.io/gorm"
)

// pgSQLDb the singleton of postgreSQL.
var pgSQLDb *gorm.DB

// initPgSQLDb init the singleton of postgreSQL.
func initPgSQLDb() *gorm.DB {
	pgSQLDb = newPostgresql()
	return pgSQLDb
}

// getPgSQLDb get the singleton of postgreSQL.
func getPgSQLDb() *gorm.DB {
	if pgSQLDb == nil {
		// execution once.
		var once sync.Once
		once.Do(func() {
			pgSQLDb = newPostgresql()
		})
	}
	return pgSQLDb
}

// pgSQLDbTxBegin get the singleton of postgreSQL by transaction.
func pgSQLDbTxBegin() *gorm.DB {
	return getPgSQLDb().Begin()
}

// newPostgresql create a new postgreSQL instance.
func newPostgresql() (pgsql *gorm.DB) {
	dsn := apollo.GetApolloConfig().PostGresSQLDsn
	pgsql, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
			logger.Config{
				SlowThreshold: time.Second,  // 慢 SQL 阈值
				LogLevel:      logger.Error, // Log level,日志级别
				Colorful:      false,        // 禁用彩色打印
			},
		),
		SkipDefaultTransaction: true, // 禁用全局事务
	})
	if err != nil {
		loggerpkg.Log().Panic(fmt.Sprintf("pgsql链接异常:%v\r\n", err))
	}
	sqlDB, _ := pgsql.DB()
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(20)
	sqlDB.SetConnMaxLifetime(30 * time.Second)
	loggerpkg.Log().Println("postgresql init...")
	return
}
