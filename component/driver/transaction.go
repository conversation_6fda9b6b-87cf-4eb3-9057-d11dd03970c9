package driver

import (
	"sync"

	"gorm.io/gorm"
)

// ITransaction interfaces export to service layer.
type ITransaction interface {
	PgSQLlDbTxBegin() *gorm.DB
	SQLServerDbTxBegin() *gorm.DB
	MysqlDbTxBegin() *gorm.DB
}

// Transaction transaction.
type Transaction struct {
}

// transaction a singleton of Transaction
var transaction ITransaction

// InitTransaction init the transaction.
func InitTransaction() ITransaction {
	transaction = &Transaction{}
	return transaction
}

// GetTransaction get the transaction.
func GetTransaction() ITransaction {
	if transaction == nil {
		// execution once.
		var once sync.Once
		once.Do(func() {
			transaction = &Transaction{}
		})
	}
	return transaction
}

// MysqlDbTxBegin get MySQL transaction.
func (t *Transaction) MysqlDbTxBegin() *gorm.DB {
	return mysqlDbTxBegin()
}

// PgSQLlDbTxBegin get PostgreSQL transaction.
func (t *Transaction) PgSQLlDbTxBegin() *gorm.DB {
	return pgSQLDbTxBegin()
}

// SQLServerDbTxBegin get SQLServer transaction.
func (t *Transaction) SQLServerDbTxBegin() *gorm.DB {
	return sqlServerDbTxBegin()
}
