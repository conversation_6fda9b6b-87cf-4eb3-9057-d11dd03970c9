package driver

import (
	"chongli/component/apollo"
	"chongli/pkg/logger"
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
)

// rdsClient the singleton of RedisClient.
var rdsClient *redis.Client

// initRdsClient init the singleton of RedisClient.
func initRdsClient() *redis.Client {
	rdsClient = newRedis()
	return rdsClient
}

// getRdsClient get the singleton of RedisClient.
func getRdsClient() *redis.Client {
	if rdsClient == nil {
		// execution once.
		var once sync.Once
		once.Do(func() {
			rdsClient = newRedis()
		})
	}
	return rdsClient
}

// newRedis create a new RedisClient instance.
func newRedis() (client *redis.Client) {
	apolloConfig := apollo.GetApolloConfig()
	dbString := apolloConfig.RedisDB
	db, errAtoi := strconv.Atoi(dbString)
	if errAtoi != nil {
		logger.Log().Panic(fmt.Sprintf("Redis db异常: %v\r\n", errAtoi))
	}
	//pass := apolloConfig.RedisPass
	client = redis.NewClient(&redis.Options{
		Addr:         apolloConfig.RedisAddr,
		Password:     apolloConfig.RedisPass,
		DB:           db,
		MaxRetries:   1,
		PoolSize:     200,
		DialTimeout:  60 * time.Second,
		ReadTimeout:  60 * time.Second,
		WriteTimeout: 60 * time.Second,
	})
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	_, err := client.Ping(ctx).Result()

	if err != nil {
		fmt.Printf("%s|||%s \r\n", logger.HTTPPort, fmt.Sprintf("redis:%s,||| %s", apolloConfig.RedisAddr, apolloConfig.RedisPass))
		logger.Log().Panic(fmt.Sprintf("Redis链接异常:%v\r\n", err))
	}
	logger.Log().Println("redis init success")
	return
}
