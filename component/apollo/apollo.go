package apollo

import (
	"chongli/pkg/logger"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"reflect"
	"strings"

	"github.com/manucorporat/try"
	"github.com/philchia/agollo/v4"
)

// apolloConfig the single apollo config.
var apolloConfig *Config

// Config the single apollo config. The apollo config name must be equal to the json tag value.
type Config struct {
	SystemConfig
	CustomerConfig
}

// SystemConfig system configs.
type SystemConfig struct {
	AiDomain string `json:"ai_domain"`
	// ClickHouse Configs.
	ClickHouseAddr string `json:"ch_addr"`
	ClickHouseDB   string `json:"ch_db"`
	ClickHouseUser string `json:"ch_user"`
	ClickHousePass string `json:"ch_pass"`

	// MySQL Configs.
	MySQLDsn string `json:"mysql_dsn"`

	// Postgresql Configs.
	PostGresSQLDsn string `json:"postgresql_dsn"`

	// Redis Configs.
	RedisAddr string `json:"redis_addr"`
	RedisDB   string `json:"redis_db"`
	RedisPass string `json:"redis_pass"`

	// SqlServer Configs.
	SQLServerDsn string `json:"sqlserver_dsn"`

	// Elastic-search Configs.
	EsAddr string `json:"es_address"`
	EsUser string `json:"es_user"`
	EsPass string `json:"es_pass"`

	// GrpcServerAddrs grpc transport configs.
	GrpcServerAddrs string `json:"grpc_server_addrs"`

	// Jwt Configs.
	JwtSecret string `json:"jwt_secret"`

	// Prometheus Configs.
	PrometheusPushDomain string `json:"prometheus_push_domain"`
	PrometheusPushJob    string `json:"prometheus_push_job"`

	// Email Server Configs.
	EmailServer string `json:"email_server_url"`

	// SLS Configs.
	SLSAccessKeyID     string `json:"sls_access_key_id"`
	SLSAccessKeySecret string `json:"sls_access_key_secret"`
	SLSEndpoint        string `json:"sls_endpoint"`
	SLSProjectName     string `json:"sls_project_name"`
	SLSLogStoreName    string `json:"sls_log_store_name"`
	SLSAllowLogLevel   string `json:"sls_allow_log_level"`
	SLSContainerName   string `json:"sls_container_name"`
	SLSImageName       string `json:"sls_image_name"`
	SLSSource          string `json:"sls_source"`

	// DingTalk Configs.
	DingTalkRobotToken string `json:"ding_talk_robot_token"`
	DingTalkKey        string `json:"ding_talk_key"`

	// aliyun Afs
	AfsAccessKeyID     string `json:"afs_access_key_id"`
	AfsAccessKeySecret string `json:"afs_access_key_secret"`
	AfsRegionID        string `json:"afs_region_id"`
	AfsEndpoint        string `json:"afs_endpoint"`
	AfsScene           string `json:"afs_scene"`
	AfsAppKey          string `json:"afs_app_key"`

	// aliyun green
	GreenAccessKeyID     string `json:"green_access_key_id"`
	GreenAccessKeySecret string `json:"green_access_key_secret"`
	GreenRegionID        string `json:"green_region_id"` // example: cn-shanghai
	//GreenEndpoint        string `json:"green_endpoint"` // example:
	GreenBizType       string `json:"green_biz_type"`             // example: mid-cloud
	GreenMiddlewareAPI string `json:"green_middleware_api"`       // example: https://cs.51wnl-cq.com/api/review
	ContentServiceName string `json:"green_content_service_name"` // green enhance content service name, example: aigcCheck_01
	ImageServiceName   string `json:"green_image_service_name"`   // green enhance image service name, example: aigcCheck_02
	VoiceServiceName   string `json:"green_voice_service_name"`   // green enhance voice service name, example: aigcCheck_02
	VideoServiceName   string `json:"green_video_service_name"`   // green enhance video service name, example: aigcCheck_02
	FileServiceName    string `json:"green_file_service_name"`    // green enhance file service name, example: aigcCheck_02
	URLServiceName     string `json:"green_url_service_name"`     // green enhance file service name, example: aigcCheck_02

	// 支付配置
	MchCode      string `json:"mch_code"`
	PartnerId    string `json:"partner_id"`
	PartnerIdKey string `json:"partnerId_key"`
	PartnerIdPwd string `json:"partnerId_pwd"`

	// 环境配置
	Env string `json:"env"`
	// 后台配置
	AdminHashKey string `json:"admin_hash_key"`

	// 七牛云配置
	QiniuyunAccessKey string `json:"qiniuyun_access_key"`
	QiniuyunSecretKey string `json:"qiniuyun_secret_key"`
	QiniuyunBucket    string `json:"qiniuyun_bucket"`
	QiniuyunDomain    string `json:"qiniuyun_domain"`

	// sms配置
	PhoneCodeSecret string `json:"phone_code_secret"`
	SmsUUID         string `json:"sms_uuid"`

	// 归因配置
	DataReportEnv string `json:"data_report_env"`

	// 个推配置
	GetuiAppId        string `json:"getui_app_id"`
	GetuiAppKey       string `json:"getui_app_key"`
	GetuiAppSecret    string `json:"getui_app_secret"`
	GetuiMasterSecret string `json:"getui_master_secret"`
}

// CustomerConfig customer configs.
type CustomerConfig struct {
	ActiveBeginTime     string `json:"active_begin_time"`
	ActiveEndTime       string `json:"active_end_time"`
	VolcengineAccessKey string `json:"volcengine_access_key"`
	VolcengineSecretKey string `json:"volcengine_secret_key"`
	ArkApiKey           string `json:"ark_api_key"`
	TencentSecretId     string `json:"tencent_secret_id"`
	TencentSecretKey    string `json:"tencent_secret_key"`
	TencentRegion       string `json:"tencent_region"`
}

const (
	// ProdEnv prod environment.
	ProdEnv = "prod"
	// TestEvn test environment.
	TestEvn = "test"
)

// grpcUpdateCallbackFunc grpc update callback function.
var grpcUpdateCallbackFunc func() error

// InitApolloConfigs init apollo configs.
func InitApolloConfigs(path ...string) {
	conf := getConf(path...)

	// set apollo configs to apolloConfig.
	mappingConfigs(conf)

	// monitor apollo config changes.
	go agollo.OnUpdate(onUpdate)
}

// SetGrpcUpdateCallback set grpc servers update callback function.
func SetGrpcUpdateCallback(updateCallback func() error) {
	grpcUpdateCallbackFunc = updateCallback
}

// GetApolloConfig get a singleton of apollo config.
func GetApolloConfig() *Config {
	return apolloConfig
}

func getConf(paths ...string) *agollo.Conf {
	apolloPath := ""
	if len(paths) > 0 {
		apolloPath = path.Join(paths[0], "apollo.properties")
	} else {
		dir, _ := os.Getwd()
		apolloPath = path.Join(dir, "/component/apollo/apollo.properties")
	}
	conf, err := agollo.NewConf(apolloPath)
	if err != nil {
		logger.Log().Panic(fmt.Sprintf("apollo 初始化异常:%v\r\n", err.Error()))
	}

	env := strings.ToLower(os.Getenv("ENVIRONMENT"))
	metaAddr := os.Getenv("APOLLO_META")
	if conf != nil {
		//if env == TestEvn {
		//	conf.MetaAddr = "http://*************:8080/"
		//}
		if env == ProdEnv {
			conf.Cluster = "staging"
		}
		conf.MetaAddr = metaAddr
		logger.Log().Println("apollo config: " + "address--" + conf.MetaAddr + ", cluster--" + conf.Cluster)
	}
	return conf
}

// mappingConfigs mapping apollo configs to structure.
func mappingConfigs(conf *agollo.Conf) {
	err := agollo.Start(conf)
	if err != nil {
		logger.Log().Panic(fmt.Sprintf("apollo 启动异常:%v\r\n", err.Error()))
	}

	configNames := agollo.GetAllKeys()

	apolloConfigMap := make(map[string]string)
	for _, name := range configNames {
		apolloConfigMap[name] = agollo.GetString(name)
	}

	// marshal apollo config map to json.
	configsBytes, errMarshall := json.Marshal(apolloConfigMap)
	if errMarshall != nil {
		logger.Log().Println(fmt.Sprintf("配置异常: %v\r\n", errMarshall))
	}

	// unmarshal json to apolloConfig.
	errUnmarshal := json.Unmarshal(configsBytes, &apolloConfig)
	if errUnmarshal != nil {
		logger.Log().Println(fmt.Sprintf("配置异常: %v\r\n", errUnmarshal))
	}
}

// onUpdate apollo config change handle.
func onUpdate(event *agollo.ChangeEvent) {
	try.This(func() {
		for _, change := range event.Changes {
			switch change.ChangeType {
			case agollo.ADD:
				// Nothing to do. There is no structure member mapping it.
			case agollo.MODIFY:
				_ = modify(change)
				if change.Key == "grpc_server_addrs" {
					// grpc config update callback.
					_ = grpcUpdateCallbackFunc()
				}
			case agollo.DELETE:
				// Set config to null string.
				change.NewValue = ""
				_ = modify(change)
			default:
				// Unknown change type.
			}
		}
	}).Finally(func() {
		// Do nothing.
	}).Catch(func(e try.E) {
		logger.Log().Println(fmt.Sprintf("apollo配置更新失败: %v\r\n", e))
	})
}

// modify modify apollo config via reflect.
func modify(change *agollo.Change) (result bool) {
	// type reflect.
	configTypeReflect := reflect.TypeOf(apolloConfig)
	// value reflect.
	configValueReflect := reflect.ValueOf(&apolloConfig).Elem()

	for index := 0; index < configTypeReflect.NumField(); index++ {
		member := configTypeReflect.Field(index)
		memberName := dfs(member, change.Key)
		if memberName != "" {
			target := configValueReflect.FieldByName(memberName)
			target.SetString(change.NewValue)
			return true
		}
	}

	return
}

// dfs deep first search to find the name of the struct member.
func dfs(root reflect.StructField, changeKey string) (memberName string) {
	switch root.Type.Kind() {
	case reflect.Struct:
		structure := root.Type
		for index := 0; index < structure.NumField(); index++ {
			memberName = dfs(structure.Field(index), changeKey)
			// Has found.
			if memberName != "" {
				return
			}
		}
	case reflect.String:
		// Has found.
		if root.Tag.Get("json") == changeKey {
			memberName = root.Name
			return
		}
	default:
		// Nothing to do.
	}

	return
}
